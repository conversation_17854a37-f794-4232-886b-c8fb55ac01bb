const fs = require('fs');
const path = require('path');

/**
 * ExecutionCollector - Collects execution information during dev mode
 * to generate mock data for tests
 */
class ExecutionCollector {
  constructor() {
    this.isDevMode = process.argv.includes('--dev');
    this.collectedData = {};
    this.outputDir = path.join(__dirname, '../tests/mock-data');
    
    // Ensure output directory exists
    if (this.isDevMode && !fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * Collect execution information for a function call
   * @param {string} functionName - Name of the function being called
   * @param {Object} params - Parameters passed to the function
   * @param {*} result - Result returned by the function
   * @param {Error} error - Error thrown by the function (if any)
   * @param {Object} context - Additional context information
   */
  collect(functionName, params = {}, result = null, error = null, context = {}) {
    if (!this.isDevMode) {
      return;
    }

    const timestamp = new Date().toISOString();
    const executionInfo = {
      timestamp,
      functionName,
      params: this.sanitizeData(params),
      result: this.sanitizeData(result),
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : null,
      context: this.sanitizeData(context)
    };

    // Store in memory
    if (!this.collectedData[functionName]) {
      this.collectedData[functionName] = [];
    }
    this.collectedData[functionName].push(executionInfo);

    // Log for debugging
    //console.log(`[ExecutionCollector] Collected data for ${functionName}:`, {
    //  params: Object.keys(params),
    //  hasResult: result !== null,
    //  hasError: error !== null,
    //  contextKeys: Object.keys(context)
    //});
  }

  /**
   * Sanitize data to remove circular references and non-serializable objects
   * @param {*} data - Data to sanitize
   * @returns {*} Sanitized data
   */
  sanitizeData(data) {
    if (data === null || data === undefined) {
      return data;
    }

    const seen = new WeakSet();
    
    const sanitize = (obj) => {
      if (obj === null || typeof obj !== 'object') {
        return obj;
      }

      if (seen.has(obj)) {
        return '[Circular Reference]';
      }
      seen.add(obj);

      // Handle special objects
      if (obj instanceof Error) {
        return {
          name: obj.name,
          message: obj.message,
          stack: obj.stack
        };
      }

      if (obj instanceof Date) {
        return obj.toISOString();
      }

      // Handle functions
      if (typeof obj === 'function') {
        return `[Function: ${obj.name || 'anonymous'}]`;
      }

      // Handle arrays
      if (Array.isArray(obj)) {
        return obj.map(item => sanitize(item));
      }

      // Handle plain objects
      const sanitized = {};
      for (const [key, value] of Object.entries(obj)) {
        try {
          sanitized[key] = sanitize(value);
        } catch (error) {
          sanitized[key] = `[Error serializing: ${error.message}]`;
        }
      }

      return sanitized;
    };

    return sanitize(data);
  }

  /**
   * Save collected data to files
   */
  async saveCollectedData() {
    if (!this.isDevMode || Object.keys(this.collectedData).length === 0) {
      return;
    }

    try {
      // Save individual function data
      for (const [functionName, executions] of Object.entries(this.collectedData)) {
        const filename = `${functionName.replace(/[^a-zA-Z0-9]/g, '_')}.json`;
        const filepath = path.join(this.outputDir, filename);
        
        await fs.promises.writeFile(
          filepath,
          JSON.stringify(executions, null, 2),
          'utf8'
        );
        
        console.log(`[ExecutionCollector] Saved ${executions.length} executions for ${functionName} to ${filename}`);
      }

      // Save summary file
      const summary = {
        generatedAt: new Date().toISOString(),
        totalFunctions: Object.keys(this.collectedData).length,
        totalExecutions: Object.values(this.collectedData).reduce((sum, arr) => sum + arr.length, 0),
        functions: Object.keys(this.collectedData).sort()
      };

      await fs.promises.writeFile(
        path.join(this.outputDir, 'summary.json'),
        JSON.stringify(summary, null, 2),
        'utf8'
      );

      console.log(`[ExecutionCollector] Saved execution summary: ${summary.totalExecutions} executions across ${summary.totalFunctions} functions`);
    } catch (error) {
      console.error('[ExecutionCollector] Failed to save collected data:', error);
    }
  }

  /**
   * Create a wrapper function that automatically collects execution data
   * @param {string} functionName - Name of the function
   * @param {Function} originalFunction - Original function to wrap
   * @param {Object} options - Options for collection
   * @returns {Function} Wrapped function
   */
  wrapFunction(functionName, originalFunction, options = {}) {
    if (!this.isDevMode) {
      return originalFunction;
    }

    const collector = this;
    
    return async function wrappedFunction(...args) {
      const startTime = Date.now();
      let result = null;
      let error = null;
      
      try {
        result = await originalFunction.apply(this, args);
        return result;
      } catch (err) {
        error = err;
        throw err;
      } finally {
        const endTime = Date.now();
        const executionTime = endTime - startTime;
        
        collector.collect(
          functionName,
          { args: args.length > 0 ? args : undefined },
          result,
          error,
          {
            executionTime,
            thisContext: options.includeThis ? this : undefined,
            ...options.additionalContext
          }
        );
      }
    };
  }

  /**
   * Get collected data for a specific function
   * @param {string} functionName - Name of the function
   * @returns {Array} Array of execution data
   */
  getCollectedData(functionName) {
    return this.collectedData[functionName] || [];
  }

  /**
   * Clear collected data
   */
  clearCollectedData() {
    this.collectedData = {};
    console.log('[ExecutionCollector] Cleared all collected data');
  }

  /**
   * Generate mock data for tests based on collected data
   * @param {string} functionName - Name of the function
   * @returns {Object} Mock data object
   */
  generateMockData(functionName) {
    const executions = this.getCollectedData(functionName);
    if (executions.length === 0) {
      return null;
    }

    // Group by similar parameters
    const mockScenarios = {};
    
    executions.forEach((execution, index) => {
      const scenarioKey = this.generateScenarioKey(execution.params);
      if (!mockScenarios[scenarioKey]) {
        mockScenarios[scenarioKey] = {
          description: `Scenario ${Object.keys(mockScenarios).length + 1}`,
          params: execution.params,
          results: []
        };
      }
      
      mockScenarios[scenarioKey].results.push({
        result: execution.result,
        error: execution.error,
        executionTime: execution.context?.executionTime,
        timestamp: execution.timestamp
      });
    });

    return {
      functionName,
      totalExecutions: executions.length,
      scenarios: mockScenarios,
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * Generate a key for grouping similar execution scenarios
   * @param {Object} params - Function parameters
   * @returns {string} Scenario key
   */
  generateScenarioKey(params) {
    if (!params || typeof params !== 'object') {
      return 'default';
    }

    // Create a simplified key based on parameter types and structure
    const keyParts = [];
    
    if (params.args && Array.isArray(params.args)) {
      keyParts.push(`args_${params.args.length}`);
      params.args.forEach((arg, index) => {
        keyParts.push(`arg${index}_${typeof arg}`);
      });
    }

    return keyParts.length > 0 ? keyParts.join('_') : 'default';
  }
}

// Create singleton instance
const executionCollector = new ExecutionCollector();

// Auto-save on process exit
if (executionCollector.isDevMode) {
  process.on('exit', () => {
    executionCollector.saveCollectedData();
  });
  
  process.on('SIGINT', async () => {
    await executionCollector.saveCollectedData();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    await executionCollector.saveCollectedData();
    process.exit(0);
  });
}

module.exports = executionCollector;
