// Mock electron modules before importing main
jest.mock('electron', () => ({
  app: {
    getPath: jest.fn().mockReturnValue('/mock/user/data'),
    getVersion: jest.fn().mockReturnValue('1.0.0'),
    whenReady: jest.fn().mockResolvedValue(),
    on: jest.fn(),
    quit: jest.fn()
  },
  BrowserWindow: jest.fn().mockImplementation(() => ({
    loadFile: jest.fn(),
    on: jest.fn(),
    once: jest.fn(),
    setMenu: jest.fn(),
    hide: jest.fn(),
    show: jest.fn(),
    minimize: jest.fn(),
    close: jest.fn(),
    setBounds: jest.fn(),
    getBounds: jest.fn().mockReturnValue({ x: 0, y: 0, width: 1920, height: 1080 }),
    setBrowserView: jest.fn(),
    webContents: {
      on: jest.fn(),
      send: jest.fn()
    }
  })),
  BrowserView: jest.fn().mockImplementation(() => ({
    webContents: {
      loadURL: jest.fn(),
      on: jest.fn(),
      executeJavaScript: jest.fn(),
      isDestroyed: jest.fn().mockReturnValue(false),
      getURL: jest.fn()
    },
    setBounds: jest.fn()
  })),
  Tray: jest.fn(),
  Menu: jest.fn(),
  ipcMain: { on: jest.fn(), handle: jest.fn() },
  screen: {
    getPrimaryDisplay: jest.fn().mockReturnValue({
      workAreaSize: { width: 1920, height: 1080 }
    })
  },
  shell: { openExternal: jest.fn() }
}));

// Mock electron-store
jest.mock('electron-store', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn()
  }));
});

// Mock auto-launch
jest.mock('auto-launch', () => {
  return jest.fn().mockImplementation(() => ({
    isEnabled: jest.fn().mockResolvedValue(false),
    enable: jest.fn().mockResolvedValue(),
    disable: jest.fn().mockResolvedValue()
  }));
});

// Mock fs module
jest.mock('fs');

// Create a mock class that only includes the methods we want to test
class MockYSViewerApp {
  constructor() {
    this.youtubeView = null;
  }

  extractVideoId(url) {
    try {
      const urlObj = new URL(url);

      // Handle youtube.com/watch?v= format
      if (urlObj.hostname.includes('youtube.com') && urlObj.pathname === '/watch') {
        return urlObj.searchParams.get('v');
      }

      // Handle youtu.be/ format
      if (urlObj.hostname === 'youtu.be') {
        return urlObj.pathname.substring(1); // Remove leading slash
      }

      return null;
    } catch (error) {
      console.error('Error extracting video ID from URL:', url, error);
      return null;
    }
  }

  async tryClickRecommendedVideo(targetUrl) {
    try {
      if (!this.youtubeView || !this.youtubeView.webContents || this.youtubeView.webContents.isDestroyed()) {
        return false;
      }

      console.log('Searching for recommended video:', targetUrl);

      // Extract video ID from target URL
      const targetVideoId = this.extractVideoId(targetUrl);
      if (!targetVideoId) {
        console.log('Could not extract video ID from URL:', targetUrl);
        return false;
      }

      // Execute JavaScript to find and click recommended video
      const clickResult = await this.youtubeView.webContents.executeJavaScript(`
        (function() {
          const targetVideoId = '${targetVideoId}';
          console.log('Looking for video ID:', targetVideoId);

          // Find all video links on the page
          const videoLinks = document.querySelectorAll('a[href*="/watch?v="], a[href*="youtu.be/"]');
          console.log('Found', videoLinks.length, 'video links on page');

          for (let link of videoLinks) {
            const href = link.href;
            let videoId = '';

            // Extract video ID from different URL formats
            if (href.includes('/watch?v=')) {
              const match = href.match(/[?&]v=([^&]+)/);
              videoId = match ? match[1] : '';
            } else if (href.includes('youtu.be/')) {
              const match = href.match(/youtu\\.be\\/([^?&]+)/);
              videoId = match ? match[1] : '';
            }

            if (videoId === targetVideoId) {
              console.log('Found matching recommended video link:', href);

              // Preserve any tracking parameters from the current page
              const currentUrl = new URL(window.location.href);
              const linkUrl = new URL(href, window.location.origin);

              // Copy tracking parameters if they exist
              const trackingParams = ['list', 'index', 'pp', 'si', 'feature', 'app', 'itct'];
              trackingParams.forEach(param => {
                if (currentUrl.searchParams.has(param)) {
                  linkUrl.searchParams.set(param, currentUrl.searchParams.get(param));
                }
              });

              // Update the link href with tracking parameters
              link.href = linkUrl.toString();

              // Click the link to navigate naturally
              console.log('Clicking recommended video with tracking:', link.href);
              link.click();
              return true;
            }
          }

          console.log('No matching recommended video found for ID:', targetVideoId);
          return false;
        })();
      `);

      if (clickResult) {
        console.log('Successfully clicked recommended video link');
        return true;
      } else {
        console.log('No matching recommended video found on current page');
        return false;
      }
    } catch (error) {
      console.error('Error trying to click recommended video:', error);
      return false;
    }
  }
}

describe('YouTube Recommended Video Detection', () => {
  let app;
  let mockYouTubeView;
  let mockWebContents;

  beforeEach(() => {
    // Mock YouTube view and web contents
    mockWebContents = {
      executeJavaScript: jest.fn(),
      isDestroyed: jest.fn().mockReturnValue(false),
      loadURL: jest.fn()
    };

    mockYouTubeView = {
      webContents: mockWebContents
    };

    // Create app instance with mocked views
    app = new MockYSViewerApp();
    app.youtubeView = mockYouTubeView;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('extractVideoId', () => {
    test('should extract video ID from youtube.com/watch URL', () => {
      const url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
      const videoId = app.extractVideoId(url);
      expect(videoId).toBe('dQw4w9WgXcQ');
    });

    test('should extract video ID from youtu.be URL', () => {
      const url = 'https://youtu.be/dQw4w9WgXcQ';
      const videoId = app.extractVideoId(url);
      expect(videoId).toBe('dQw4w9WgXcQ');
    });

    test('should handle URLs with additional parameters', () => {
      const url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ&list=PLrAXtmRdnEQy6nuLMt9xUCce0-WvQvckS&index=1';
      const videoId = app.extractVideoId(url);
      expect(videoId).toBe('dQw4w9WgXcQ');
    });

    test('should return null for invalid URLs', () => {
      const url = 'https://example.com/invalid';
      const videoId = app.extractVideoId(url);
      expect(videoId).toBeNull();
    });

    test('should handle malformed URLs gracefully', () => {
      const url = 'not-a-url';
      const videoId = app.extractVideoId(url);
      expect(videoId).toBeNull();
    });
  });

  describe('tryClickRecommendedVideo', () => {
    test('should find and click matching recommended video', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
      
      // Mock successful JavaScript execution
      mockWebContents.executeJavaScript.mockResolvedValue(true);

      const result = await app.tryClickRecommendedVideo(targetUrl);

      expect(result).toBe(true);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining("const targetVideoId = 'dQw4w9WgXcQ'")
      );
    });

    test('should return false when no matching video found', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=notfound123';
      
      // Mock JavaScript execution returning false (no match found)
      mockWebContents.executeJavaScript.mockResolvedValue(false);

      const result = await app.tryClickRecommendedVideo(targetUrl);

      expect(result).toBe(false);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalled();
    });

    test('should handle destroyed web contents gracefully', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
      
      // Mock destroyed web contents
      mockWebContents.isDestroyed.mockReturnValue(true);

      const result = await app.tryClickRecommendedVideo(targetUrl);

      expect(result).toBe(false);
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should handle JavaScript execution errors', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
      
      // Mock JavaScript execution error
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('JavaScript error'));

      const result = await app.tryClickRecommendedVideo(targetUrl);

      expect(result).toBe(false);
    });

    test('should handle invalid target URLs', async () => {
      const targetUrl = 'invalid-url';

      const result = await app.tryClickRecommendedVideo(targetUrl);

      expect(result).toBe(false);
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });
  });

  describe('JavaScript execution for recommended videos', () => {
    test('should generate correct JavaScript for video detection', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=testVideoId';
      
      mockWebContents.executeJavaScript.mockResolvedValue(true);

      await app.tryClickRecommendedVideo(targetUrl);

      const jsCode = mockWebContents.executeJavaScript.mock.calls[0][0];
      
      // Verify the JavaScript contains the correct video ID
      expect(jsCode).toContain("const targetVideoId = 'testVideoId'");
      
      // Verify it looks for video links
      expect(jsCode).toContain('document.querySelectorAll(\'a[href*="/watch?v="], a[href*="youtu.be/"]\'');
      
      // Verify it handles tracking parameters
      expect(jsCode).toContain('trackingParams');
      expect(jsCode).toContain('list');
      expect(jsCode).toContain('index');
      expect(jsCode).toContain('pp');
      expect(jsCode).toContain('si');
      expect(jsCode).toContain('feature');
      expect(jsCode).toContain('app');
      expect(jsCode).toContain('itct');
      
      // Verify it clicks the link
      expect(jsCode).toContain('link.click()');
    });
  });

  describe('Mock Data Collection', () => {
    test('should collect execution data for recommended video detection', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=mockVideo123';
      
      // Mock various scenarios
      const scenarios = [
        { found: true, clicked: true },
        { found: true, clicked: false },
        { found: false, clicked: false }
      ];

      const executionData = [];

      for (const scenario of scenarios) {
        const startTime = Date.now();
        mockWebContents.executeJavaScript.mockResolvedValue(scenario.clicked);
        
        const result = await app.tryClickRecommendedVideo(targetUrl);
        const endTime = Date.now();
        
        executionData.push({
          scenario: scenario,
          result: result,
          executionTime: endTime - startTime,
          success: result === scenario.clicked
        });
      }

      // Verify we collected data for all scenarios
      expect(executionData).toHaveLength(3);
      
      // Calculate success rate
      const successfulExecutions = executionData.filter(data => data.success).length;
      const successRate = successfulExecutions / executionData.length;
      
      expect(successRate).toBeGreaterThanOrEqual(0);
      expect(successRate).toBeLessThanOrEqual(1);
      
      console.log('Recommended video detection execution data:', {
        totalExecutions: executionData.length,
        successfulExecutions: successfulExecutions,
        successRate: successRate,
        averageExecutionTime: executionData.reduce((sum, data) => sum + data.executionTime, 0) / executionData.length,
        scenarios: executionData.map(data => ({
          scenario: data.scenario,
          result: data.result,
          executionTime: data.executionTime
        }))
      });
    });
  });
});
