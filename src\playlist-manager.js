const Store = require('electron-store');

class PlaylistManager {
  constructor() {
    this.store = new Store();
    this.fetch = null;
    this.playlistData = {
      mandatory: [],
      randomPool: [],
      dailyStats: {},
      lastUpdated: null
    };
    this.s3Config = {
      bucket: 'ysviewertests',
      region: 'eu-west-1',
      key: 'video_list.txt'
    };
  }

  async initFetch() {
    try {
      const { default: fetch } = await import('node-fetch');
      this.fetch = fetch;
    } catch (error) {
      console.error('Failed to load node-fetch:', error);
    }
  }

  async initialize() {
    try {
      // Wait for fetch to be loaded
      if (!this.fetch) {
        await this.initFetch();
      }

      // Load cached playlist data
      const cachedData = this.store.get('playlistData');
      if (cachedData) {
        this.playlistData = { ...this.playlistData, ...cachedData };
      }

      // Download latest playlist from S3
      await this.downloadPlaylist();
      
      // Reset daily counters if it's a new day
      this.checkAndResetDailyCounters();
      
      console.log('Playlist manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize playlist manager:', error);
      // Use cached data if available
      if (this.playlistData.mandatory.length === 0) {
        this.loadDefaultPlaylist();
      }
    }
  }

  async downloadPlaylist() {
    try {
      const url = `https://${this.s3Config.bucket}.s3.${this.s3Config.region}.amazonaws.com/${this.s3Config.key}`;
      console.log('Downloading playlist from:', url);
      
      const response = await this.fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const content = await response.text();
      this.parsePlaylistContent(content);
      
      // Cache the data
      this.store.set('playlistData', this.playlistData);
      
      console.log('Playlist downloaded and parsed successfully');
    } catch (error) {
      console.error('Failed to download playlist:', error);
      throw error;
    }
  }

  parsePlaylistContent(content) {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    let currentSection = '';
    
    this.playlistData.mandatory = [];
    this.playlistData.randomPool = [];
    
    for (const line of lines) {
      if (line.toLowerCase().includes('mandatory')) {
        currentSection = 'mandatory';
        continue;
      } else if (line.toLowerCase().includes('randomly played')) {
        currentSection = 'random';
        continue;
      }
      
      if (line.startsWith('http')) {
        const videoEntry = {
          url: line,
          playCountToday: 0,
          durationWatched: 0,
          lastPlayed: null,
          platform: this.detectPlatform(line)
        };
        
        if (currentSection === 'mandatory') {
          this.playlistData.mandatory.push(videoEntry);
        } else if (currentSection === 'random') {
          this.playlistData.randomPool.push(videoEntry);
        }
      }
    }
    
    this.playlistData.lastUpdated = new Date().toISOString();
  }

  detectPlatform(url) {
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      return 'YouTube';
    } else if (url.includes('spotify.com')) {
      return 'Spotify';
    }
    return 'Unknown';
  }

  checkAndResetDailyCounters() {
    const today = new Date().toISOString().split('T')[0];
    const lastResetDate = this.store.get('lastResetDate');
    
    if (lastResetDate !== today) {
      // Reset daily counters
      this.playlistData.mandatory.forEach(entry => {
        entry.playCountToday = 0;
      });
      
      this.playlistData.randomPool.forEach(entry => {
        entry.playCountToday = 0;
      });
      
      // Initialize daily stats
      if (!this.playlistData.dailyStats[today]) {
        this.playlistData.dailyStats[today] = {
          mandatoryPlays: 0,
          randomPlays: 0,
          date: today
        };
      }
      
      this.store.set('lastResetDate', today);
      this.store.set('playlistData', this.playlistData);
      
      console.log('Daily counters reset for', today);
    }
  }

  async getNextMandatoryVideo(preferredPlatform = null) {
    // Find videos that need to be played (less than 3 times today)
    let availableVideos = this.playlistData.mandatory.filter(entry =>
      entry.playCountToday < 3
    );

    // Filter by platform if specified
    if (preferredPlatform) {
      const platformVideos = availableVideos.filter(entry =>
        entry.platform === preferredPlatform
      );

      // Use platform-specific videos if available, otherwise fall back to all
      if (platformVideos.length > 0) {
        availableVideos = platformVideos;
      }
    }

    if (availableVideos.length === 0) {
      // All mandatory videos played 3 times, try random videos
      return this.getRandomVideo();
    }

    // Sort by play count (ascending) and last played time
    availableVideos.sort((a, b) => {
      if (a.playCountToday !== b.playCountToday) {
        return a.playCountToday - b.playCountToday;
      }

      const aTime = a.lastPlayed ? new Date(a.lastPlayed).getTime() : 0;
      const bTime = b.lastPlayed ? new Date(b.lastPlayed).getTime() : 0;
      return aTime - bTime;
    });

    // Instead of always selecting the first video, randomly select from the least played videos
    const lowestPlayCount = availableVideos[0].playCountToday;
    const leastPlayedVideos = availableVideos.filter(video => video.playCountToday === lowestPlayCount);

    // Randomly select from videos with the same lowest play count
    const randomIndex = Math.floor(Math.random() * leastPlayedVideos.length);
    const selectedVideo = leastPlayedVideos[randomIndex];

    this.updatePlayCount(selectedVideo);

    return selectedVideo;
  }

  getRandomVideo() {
    const today = new Date().toISOString().split('T')[0];
    const dailyStats = this.playlistData.dailyStats[today] || { randomPlays: 0 };

    // Check if we've reached 70% of random videos for today
    const maxRandomPlays = Math.ceil(this.playlistData.randomPool.length * 0.7);

    if (dailyStats.randomPlays >= maxRandomPlays) {
      return null; // No more random videos for today
    }

    // Get videos that haven't been played today
    let availableVideos = this.playlistData.randomPool.filter(entry =>
      entry.playCountToday === 0
    );

    if (availableVideos.length === 0) {
      return null;
    }

    // Shuffle the available videos to ensure true randomness
    availableVideos = this.shuffleArray([...availableVideos]);

    // Select random video from shuffled array
    const randomIndex = Math.floor(Math.random() * availableVideos.length);
    const selectedVideo = availableVideos[randomIndex];

    this.updatePlayCount(selectedVideo, true);

    return selectedVideo;
  }

  /**
   * Shuffle an array using Fisher-Yates algorithm for true randomness
   * @param {Array} array - Array to shuffle
   * @returns {Array} Shuffled array
   */
  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Get a truly random video from any available pool
   * @param {string} preferredPlatform - Optional platform preference
   * @returns {Object|null} Random video or null if none available
   */
  getAnyRandomVideo(preferredPlatform = null) {
    // Combine all available videos from both pools
    const allAvailableVideos = [];

    // Add mandatory videos that haven't reached their daily limit
    const availableMandatory = this.playlistData.mandatory.filter(entry =>
      entry.playCountToday < 3
    );
    allAvailableVideos.push(...availableMandatory);

    // Add random pool videos that haven't been played today
    const availableRandom = this.playlistData.randomPool.filter(entry =>
      entry.playCountToday === 0
    );
    allAvailableVideos.push(...availableRandom);

    if (allAvailableVideos.length === 0) {
      return null;
    }

    // Filter by platform if specified
    let filteredVideos = allAvailableVideos;
    if (preferredPlatform) {
      const platformVideos = allAvailableVideos.filter(entry =>
        entry.platform === preferredPlatform
      );
      if (platformVideos.length > 0) {
        filteredVideos = platformVideos;
      }
    }

    // Shuffle and select random video
    const shuffledVideos = this.shuffleArray(filteredVideos);
    const randomIndex = Math.floor(Math.random() * shuffledVideos.length);
    const selectedVideo = shuffledVideos[randomIndex];

    // Determine if it's from random pool
    const isFromRandomPool = this.playlistData.randomPool.includes(selectedVideo);
    this.updatePlayCount(selectedVideo, isFromRandomPool);

    return selectedVideo;
  }

  updatePlayCount(video, isRandom = false) {
    video.playCountToday++;
    video.lastPlayed = new Date().toISOString();
    
    const today = new Date().toISOString().split('T')[0];
    if (!this.playlistData.dailyStats[today]) {
      this.playlistData.dailyStats[today] = {
        mandatoryPlays: 0,
        randomPlays: 0,
        date: today
      };
    }
    
    if (isRandom) {
      this.playlistData.dailyStats[today].randomPlays++;
    } else {
      this.playlistData.dailyStats[today].mandatoryPlays++;
    }
    
    // Save to store
    this.store.set('playlistData', this.playlistData);
  }

  async shouldPlayNext() {
    // Check if it's time to play the next video (every hour)
    const lastPlayTime = this.store.get('lastPlayTime', 0);
    const now = Date.now();
    const hourInMs = 60 * 60 * 1000;
    
    if (now - lastPlayTime >= hourInMs) {
      this.store.set('lastPlayTime', now);
      return true;
    }
    
    return false;
  }

  updateWatchProgress(url, progress) {
    // Find the video and update watch progress
    const allVideos = [...this.playlistData.mandatory, ...this.playlistData.randomPool];
    const video = allVideos.find(v => v.url === url);
    
    if (video) {
      video.durationWatched = Math.max(video.durationWatched, progress);
      this.store.set('playlistData', this.playlistData);
    }
  }

  loadDefaultPlaylist() {
    // Fallback playlist if S3 download fails
    this.playlistData = {
      mandatory: [
        {
          url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
          playCountToday: 0,
          durationWatched: 0,
          lastPlayed: null,
          platform: 'YouTube'
        }
      ],
      randomPool: [
        {
          url: 'https://www.youtube.com/watch?v=9bZkp7q19f0',
          playCountToday: 0,
          durationWatched: 0,
          lastPlayed: null,
          platform: 'YouTube'
        }
      ],
      dailyStats: {},
      lastUpdated: new Date().toISOString()
    };
    
    console.log('Loaded default playlist');
  }

  getPlaylistStats() {
    const today = new Date().toISOString().split('T')[0];
    const todayStats = this.playlistData.dailyStats[today] || { mandatoryPlays: 0, randomPlays: 0 };
    
    return {
      mandatoryTotal: this.playlistData.mandatory.length,
      randomTotal: this.playlistData.randomPool.length,
      mandatoryPlayedToday: todayStats.mandatoryPlays,
      randomPlayedToday: todayStats.randomPlays,
      lastUpdated: this.playlistData.lastUpdated
    };
  }
}

module.exports = PlaylistManager;
