const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');
const mockDataLoader = require('./mock-data-loader');

// Mock web contents
const mockWebContents = {
  executeJavaScript: jest.fn(),
  isDestroyed: jest.fn(() => false)
};

// Import the class to test
const YouTubeAdSkipper = require('../src/youtube-ad-skipper');

describe('YouTubeAdSkipper', () => {
  let adSkipper;

  beforeEach(() => {
    adSkipper = new YouTubeAdSkipper();
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useFakeTimers();

    // Reset mock implementations
    mockWebContents.executeJavaScript.mockReset();
    mockWebContents.isDestroyed.mockReturnValue(false);
  });

  afterEach(() => {
    if (adSkipper) {
      adSkipper.stopAdDetection();
    }
    jest.useRealTimers();
  });

  describe('initialization', () => {
    test('should initialize with default values', () => {
      expect(adSkipper.isEnabled).toBe(true);
      expect(adSkipper.adSkippedCount).toBe(0);
      expect(adSkipper.webContents).toBe(null);
      expect(adSkipper.checkInterval).toBe(null);
      expect(adSkipper.consecutiveAdDetections).toBe(0);
    });

    test('should have correct detection interval', () => {
      expect(adSkipper.adDetectionInterval).toBe(500);
    });
  });

  describe('registerWebContents', () => {
    test('should register web contents and start detection', () => {
      const startDetectionSpy = jest.spyOn(adSkipper, 'startAdDetection');
      
      adSkipper.registerWebContents(mockWebContents);
      
      expect(adSkipper.webContents).toBe(mockWebContents);
      expect(startDetectionSpy).toHaveBeenCalled();
    });

    test('should log registration message', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      adSkipper.registerWebContents(mockWebContents);
      
      expect(consoleSpy).toHaveBeenCalledWith('YouTube Ad Skipper: Web contents registered');
      consoleSpy.mockRestore();
    });
  });

  describe('unregisterWebContents', () => {
    test('should unregister web contents and stop detection', () => {
      adSkipper.registerWebContents(mockWebContents);
      const stopDetectionSpy = jest.spyOn(adSkipper, 'stopAdDetection');
      
      adSkipper.unregisterWebContents();
      
      expect(adSkipper.webContents).toBe(null);
      expect(stopDetectionSpy).toHaveBeenCalled();
    });
  });

  describe('startAdDetection', () => {
    test('should start interval when enabled and web contents registered', () => {
      adSkipper.registerWebContents(mockWebContents);
      
      expect(adSkipper.checkInterval).not.toBe(null);
    });

    test('should not start interval when disabled', () => {
      adSkipper.isEnabled = false;
      adSkipper.registerWebContents(mockWebContents);
      
      expect(adSkipper.checkInterval).toBe(null);
    });

    test('should not start interval when no web contents', () => {
      adSkipper.startAdDetection();
      
      expect(adSkipper.checkInterval).toBe(null);
    });

    test('should clear existing interval before starting new one', () => {
      adSkipper.registerWebContents(mockWebContents);
      const firstInterval = adSkipper.checkInterval;
      
      adSkipper.startAdDetection();
      
      expect(adSkipper.checkInterval).not.toBe(firstInterval);
    });
  });

  describe('stopAdDetection', () => {
    test('should clear interval and set to null', () => {
      adSkipper.registerWebContents(mockWebContents);
      
      adSkipper.stopAdDetection();
      
      expect(adSkipper.checkInterval).toBe(null);
    });

    test('should handle null interval gracefully', () => {
      expect(() => adSkipper.stopAdDetection()).not.toThrow();
    });
  });

  describe('checkForAds', () => {
    beforeEach(() => {
      adSkipper.registerWebContents(mockWebContents);
    });

    test('should return early if web contents is destroyed', async () => {
      mockWebContents.isDestroyed.mockReturnValue(true);
      
      await adSkipper.checkForAds();
      
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should return early if currently skipping', async () => {
      adSkipper.isCurrentlySkipping = true;
      
      await adSkipper.checkForAds();
      
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should execute JavaScript to detect ads', async () => {
      adSkipper.registerWebContents(mockWebContents);

      mockWebContents.executeJavaScript.mockResolvedValue({
        isAdPlaying: false,
        skipButtonAvailable: false,
        adType: 'none',
        debug: { totalButtons: 0, skipButtons: [] }
      });

      await adSkipper.checkForAds();

      expect(mockWebContents.executeJavaScript).toHaveBeenCalled();
    });

    test('should skip ad when ad is playing and skip button is available', async () => {
      adSkipper.registerWebContents(mockWebContents);

      mockWebContents.executeJavaScript.mockResolvedValue({
        isAdPlaying: true,
        skipButtonAvailable: true,
        skipButtonSelector: 'ytp-ad-skip-button',
        skipButtonText: 'Skip Ad',
        adType: 'overlay',
        debug: { totalButtons: 5, skipButtons: [{ selector: '.ytp-skip-ad-button', visible: true }] }
      });

      const skipAdSpy = jest.spyOn(adSkipper, 'skipAd').mockResolvedValue();

      // Need to set consecutive detections to trigger skip
      adSkipper.consecutiveAdDetections = 2;

      await adSkipper.checkForAds();

      expect(skipAdSpy).toHaveBeenCalled();
    });

    test('should not skip ad when ad is playing but skip button not available', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue({
        isAdPlaying: true,
        skipButtonAvailable: false,
        adType: 'overlay',
        debug: { totalButtons: 3, skipButtons: [] }
      });

      const skipAdSpy = jest.spyOn(adSkipper, 'skipAd').mockResolvedValue();

      await adSkipper.checkForAds();

      expect(skipAdSpy).not.toHaveBeenCalled();
    });

    test('should handle JavaScript execution errors gracefully', async () => {
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('Test error'));
      
      await expect(adSkipper.checkForAds()).resolves.not.toThrow();
    });

    test('should stop detection if web contents is destroyed', async () => {
      adSkipper.registerWebContents(mockWebContents);

      mockWebContents.executeJavaScript.mockRejectedValue(new Error('Object has been destroyed'));
      const stopDetectionSpy = jest.spyOn(adSkipper, 'stopAdDetection');

      await adSkipper.checkForAds();

      expect(stopDetectionSpy).toHaveBeenCalled();
    });
  });

  describe('skipAd', () => {
    beforeEach(() => {
      adSkipper.registerWebContents(mockWebContents);
    });

    test('should return early if currently skipping', async () => {
      adSkipper.isCurrentlySkipping = true;
      
      await adSkipper.skipAd();
      
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should execute JavaScript to click skip button', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue({ success: true, method: 'test' });

      await adSkipper.skipAd();

      expect(mockWebContents.executeJavaScript).toHaveBeenCalled();
    });

    test('should increment skip count when successful', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue({ success: true, method: 'test', buttonText: 'Skip' });
      const initialCount = adSkipper.adSkippedCount;

      await adSkipper.skipAd();

      expect(adSkipper.adSkippedCount).toBe(initialCount + 1);
    });

    test('should update last skip time when successful', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue({ success: true, method: 'test', buttonText: 'Skip' });
      const beforeTime = Date.now();

      await adSkipper.skipAd();

      expect(adSkipper.lastAdSkipTime).toBeGreaterThanOrEqual(beforeTime);
    });

    test('should not increment count when skip fails', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue({ success: false, reason: 'No button found' });
      const initialCount = adSkipper.adSkippedCount;

      await adSkipper.skipAd();

      expect(adSkipper.adSkippedCount).toBe(initialCount);
    });

    test('should handle JavaScript execution errors', async () => {
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('Test error'));
      
      await expect(adSkipper.skipAd()).resolves.not.toThrow();
    });

    test('should set isCurrentlySkipping flag during execution', async () => {
      let isSkippingDuringExecution = false;
      mockWebContents.executeJavaScript.mockImplementation(() => {
        isSkippingDuringExecution = adSkipper.isCurrentlySkipping;
        return Promise.resolve(true);
      });
      
      await adSkipper.skipAd();
      
      expect(isSkippingDuringExecution).toBe(true);
    });
  });

  describe('enable/disable', () => {
    test('should enable ad skipping and start detection', () => {
      adSkipper.isEnabled = false;
      adSkipper.registerWebContents(mockWebContents);
      const startDetectionSpy = jest.spyOn(adSkipper, 'startAdDetection');
      
      adSkipper.enable();
      
      expect(adSkipper.isEnabled).toBe(true);
      expect(startDetectionSpy).toHaveBeenCalled();
    });

    test('should disable ad skipping and stop detection', () => {
      adSkipper.registerWebContents(mockWebContents);
      const stopDetectionSpy = jest.spyOn(adSkipper, 'stopAdDetection');
      
      adSkipper.disable();
      
      expect(adSkipper.isEnabled).toBe(false);
      expect(stopDetectionSpy).toHaveBeenCalled();
    });
  });

  describe('getStats', () => {
    test('should return current statistics', () => {
      adSkipper.adSkippedCount = 5;
      adSkipper.lastAdSkipTime = 12345;
      adSkipper.isCurrentlySkipping = true;
      
      const stats = adSkipper.getStats();
      
      expect(stats).toEqual({
        isEnabled: true,
        adSkippedCount: 5,
        lastAdSkipTime: 12345,
        isCurrentlySkipping: true
      });
    });
  });

  describe('resetStats', () => {
    test('should reset statistics to zero', () => {
      adSkipper.adSkippedCount = 10;
      adSkipper.lastAdSkipTime = 54321;

      adSkipper.resetStats();

      expect(adSkipper.adSkippedCount).toBe(0);
      expect(adSkipper.lastAdSkipTime).toBe(0);
    });
  });

  describe('Mock Data Collection', () => {
    beforeEach(() => {
      adSkipper.registerWebContents(mockWebContents);
    });

    test('should collect ad detection history', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue({
        isAdPlaying: true,
        skipButtonAvailable: false,
        adType: 'overlay',
        skipButtonText: '',
        adTimeRemaining: '5s',
        debug: { totalButtons: 3, skipButtons: [] }
      });

      await adSkipper.checkForAds();

      const history = adSkipper.getAdDetectionHistory();
      expect(history.length).toBeGreaterThan(0);
      expect(history[0]).toHaveProperty('timestamp');
      expect(history[0]).toHaveProperty('isAdPlaying', true);
      expect(history[0]).toHaveProperty('adType', 'overlay');
    });

    test('should collect skip attempt history', async () => {
      mockWebContents.executeJavaScript
        .mockResolvedValueOnce({
          isAdPlaying: true,
          skipButtonAvailable: true,
          adType: 'overlay',
          skipButtonText: 'Skip Ad',
          debug: { totalButtons: 5, skipButtons: [{ selector: '.ytp-skip-ad-button', visible: true }] }
        })
        .mockResolvedValueOnce({
          success: true,
          method: 'button-click'
        });

      // Set consecutive detections to trigger skip
      adSkipper.consecutiveAdDetections = 2;
      await adSkipper.checkForAds();

      const history = adSkipper.getSkipAttemptHistory();
      expect(history.length).toBeGreaterThan(0);
      expect(history[0]).toHaveProperty('timestamp');
      expect(history[0]).toHaveProperty('success', true);
      expect(history[0]).toHaveProperty('method', 'button-click');
    });

    test('should limit ad detection history to 50 entries', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue({
        isAdPlaying: false,
        skipButtonAvailable: false,
        adType: 'none',
        debug: { totalButtons: 0, skipButtons: [] }
      });

      // Add 60 detection entries
      for (let i = 0; i < 60; i++) {
        await adSkipper.checkForAds();
      }

      const history = adSkipper.getAdDetectionHistory();
      expect(history.length).toBe(50);
    });

    test('should limit skip attempt history to 30 entries', async () => {
      mockWebContents.executeJavaScript
        .mockResolvedValue({
          isAdPlaying: true,
          skipButtonAvailable: true,
          adType: 'overlay',
          skipButtonText: 'Skip Ad',
          debug: { totalButtons: 5, skipButtons: [{ selector: '.ytp-skip-ad-button', visible: true }] }
        });

      // Mock successful skip attempts
      const skipAdSpy = jest.spyOn(adSkipper, 'skipAd').mockImplementation(async () => {
        adSkipper.skipAttemptHistory.push({
          timestamp: Date.now(),
          success: true,
          method: 'test'
        });
        // Apply the same trimming logic as the real method
        if (adSkipper.skipAttemptHistory.length > 30) {
          adSkipper.skipAttemptHistory = adSkipper.skipAttemptHistory.slice(-30);
        }
      });

      // Add 35 skip attempts
      for (let i = 0; i < 35; i++) {
        adSkipper.consecutiveAdDetections = 2;
        await adSkipper.checkForAds();
      }

      const history = adSkipper.getSkipAttemptHistory();
      expect(history.length).toBe(30);

      skipAdSpy.mockRestore();
    });

    test('should provide comprehensive mock data', () => {
      adSkipper.adDetectionHistory = [
        { timestamp: Date.now(), isAdPlaying: true, adType: 'overlay' },
        { timestamp: Date.now(), isAdPlaying: false, adType: 'none' }
      ];
      adSkipper.skipAttemptHistory = [
        { timestamp: Date.now(), success: true, method: 'button-click' }
      ];

      const mockData = adSkipper.getMockData();

      expect(mockData).toHaveProperty('adDetectionHistory');
      expect(mockData).toHaveProperty('skipAttemptHistory');
      expect(mockData).toHaveProperty('stats');
      expect(mockData).toHaveProperty('generatedAt');
      expect(mockData.adDetectionHistory.length).toBe(2);
      expect(mockData.skipAttemptHistory.length).toBe(1);
    });

    test('should clear mock data', () => {
      adSkipper.adDetectionHistory = [{ timestamp: Date.now() }];
      adSkipper.skipAttemptHistory = [{ timestamp: Date.now() }];

      adSkipper.clearMockData();

      expect(adSkipper.adDetectionHistory).toEqual([]);
      expect(adSkipper.skipAttemptHistory).toEqual([]);
    });
  });

  describe('Mock Data Integration Tests', () => {
    beforeEach(() => {
      adSkipper.registerWebContents(mockWebContents);
    });

    test('should use collected mock data for ad detection testing', async () => {
      // Try to load mock data for checkForAds
      const mockData = mockDataLoader.getMockDataForScenario('checkForAds');

      if (mockData && mockData.results.length > 0) {
        // Use real mock data result
        const mockResult = mockData.results[0].result;
        mockWebContents.executeJavaScript.mockResolvedValue(mockResult);

        await adSkipper.checkForAds();

        expect(mockWebContents.executeJavaScript).toHaveBeenCalled();

        if (mockResult && mockResult.isAdPlaying) {
          const history = adSkipper.getAdDetectionHistory();
          expect(history.length).toBeGreaterThan(0);
          expect(history[0].isAdPlaying).toBe(mockResult.isAdPlaying);
        }
      } else {
        // Fallback test with simulated data
        mockWebContents.executeJavaScript.mockResolvedValue({
          isAdPlaying: true,
          skipButtonAvailable: true,
          adType: 'overlay',
          skipButtonText: 'Skip Ad',
          debug: { totalButtons: 5, skipButtons: [] }
        });

        await adSkipper.checkForAds();
        const history = adSkipper.getAdDetectionHistory();
        expect(history.length).toBe(1);
      }
    });

    test('should verify ad detection accuracy with mock data', async () => {
      const detectionResults = [];

      // Test various ad detection scenarios
      const testScenarios = [
        { isAdPlaying: true, skipButtonAvailable: true, adType: 'overlay' },
        { isAdPlaying: true, skipButtonAvailable: false, adType: 'video-src' },
        { isAdPlaying: false, skipButtonAvailable: false, adType: 'none' },
        { isAdPlaying: true, skipButtonAvailable: true, adType: 'text' }
      ];

      for (const scenario of testScenarios) {
        mockWebContents.executeJavaScript.mockResolvedValue({
          ...scenario,
          debug: { totalButtons: scenario.skipButtonAvailable ? 5 : 0, skipButtons: [] }
        });

        await adSkipper.checkForAds();
        detectionResults.push(scenario);
      }

      const history = adSkipper.getAdDetectionHistory();
      expect(history.length).toBe(testScenarios.length);

      // Verify detection accuracy
      history.forEach((detection, index) => {
        expect(detection.isAdPlaying).toBe(testScenarios[index].isAdPlaying);
        expect(detection.adType).toBe(testScenarios[index].adType);
      });
    });

    test('should verify skip success rates with mock data', async () => {
      // Test various skip scenarios by directly calling skipAd
      const skipScenarios = [
        { success: true, method: 'button-click' },
        { success: true, method: 'video-manipulation' },
        { success: false, method: null },
        { success: true, method: 'button-click' }
      ];

      // Mock skipAd calls directly
      for (const scenario of skipScenarios) {
        mockWebContents.executeJavaScript.mockResolvedValueOnce(scenario);
        adSkipper.isCurrentlySkipping = false; // Reset flag for each test
        await adSkipper.skipAd();
      }

      const history = adSkipper.getSkipAttemptHistory();
      expect(history.length).toBe(skipScenarios.length);

      // Calculate success rate
      const successfulSkips = history.filter(attempt => attempt.success).length;
      const successRate = successfulSkips / history.length;

      expect(successRate).toBeGreaterThan(0); // Should have some successful skips
      expect(successRate).toBeLessThanOrEqual(1); // Should not exceed 100%
    });
  });
});
