#!/usr/bin/env node

/**
 * Test script to verify mute functionality
 * Run this while the application is running to test mute state
 */

const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

async function testMuteState() {
  console.log('🔍 Testing YSViewer Mute State...\n');
  
  try {
    // Check if YSViewer process is running
    const { stdout } = await execAsync('tasklist /FI "IMAGENAME eq electron.exe" /FO CSV');
    const electronProcesses = stdout.split('\n').filter(line => line.includes('electron.exe'));
    
    if (electronProcesses.length === 0) {
      console.log('❌ YSViewer (Electron) is not running');
      console.log('   Please start the application first with: npm run dev');
      return;
    }
    
    console.log('✅ YSViewer is running');
    console.log(`   Found ${electronProcesses.length} Electron process(es)`);
    
    // Test system audio level
    console.log('\n🔊 Checking system audio...');
    
    try {
      const volumeScript = `
        Add-Type -AssemblyName System.Windows.Forms
        $wmp = New-Object -ComObject WMPlayer.OCX
        [Math]::Round($wmp.settings.volume)
      `;
      
      const { stdout: volumeOutput } = await execAsync(`powershell -Command "${volumeScript}"`);
      const systemVolume = parseInt(volumeOutput.trim());
      
      console.log(`   System volume level: ${systemVolume}%`);
      
      if (systemVolume === 0) {
        console.log('   ⚠️  System is muted - this might affect application audio');
      } else {
        console.log('   ✅ System audio is not muted');
      }
      
    } catch (volumeError) {
      console.log('   ⚠️  Could not check system volume:', volumeError.message);
    }
    
    // Instructions for manual testing
    console.log('\n📋 Manual Testing Instructions:');
    console.log('   1. Look at the YSViewer window');
    console.log('   2. Check if the audio button shows 🔇 (muted) or 🔊 (unmuted)');
    console.log('   3. Listen for any audio from YouTube or Spotify');
    console.log('   4. Press Ctrl+V in the YSViewer window to verify mute state in console');
    console.log('   5. Click the audio button to toggle mute/unmute');
    
    console.log('\n🎯 Expected Behavior:');
    console.log('   - Application should start MUTED by default');
    console.log('   - Audio button should show 🔇 and say "Unmute"');
    console.log('   - No sound should come from YouTube or Spotify');
    console.log('   - Console should show "Web content actual mute state: true"');
    
    console.log('\n🔧 If you still hear sound:');
    console.log('   1. Try clicking the mute button to toggle it');
    console.log('   2. Check if any browser tabs outside YSViewer are playing audio');
    console.log('   3. Check Windows Volume Mixer for YSViewer-specific volume');
    console.log('   4. Restart the application');
    
  } catch (error) {
    console.error('❌ Error testing mute state:', error.message);
  }
}

// Run the test
testMuteState().catch(console.error);
