# Execution Data Collection System

This document describes the execution data collection system that automatically generates mock data for tests based on real application execution.

## Overview

The execution data collection system captures function calls, parameters, results, and execution context during development mode. This data is then used to generate realistic mock data for tests, ensuring that test behavior closely matches real application behavior.

## Components

### 1. ExecutionCollector (`src/execution-collector.js`)

The main component that collects execution information during dev mode.

**Features:**
- Automatically detects dev mode (`--dev` flag)
- Collects function parameters, results, errors, and execution context
- Sanitizes data to remove circular references and non-serializable objects
- Saves collected data to JSON files
- Provides function wrapping utilities

**Usage:**
```javascript
const executionCollector = require('./execution-collector');

// Manual collection
executionCollector.collect('functionName', params, result, error, context);

// Function wrapping (automatic collection)
const wrappedFunction = executionCollector.wrapFunction('functionName', originalFunction);
```

### 2. MockDataGenerator (`tests/mock-data-generator.js`)

Generates Jest mock functions and files based on collected execution data.

**Features:**
- Loads execution data from JSON files
- Groups executions by similar scenarios
- Generates Jest mock configurations
- Creates complete mock files with setup utilities

**Usage:**
```javascript
const MockDataGenerator = require('./mock-data-generator');
const generator = new MockDataGenerator();

// Generate mock for specific function
const mockConfig = generator.generateJestMock('functionName');

// Generate all available mocks
generator.generateAllMocks();
```

### 3. MockDataLoader (`tests/mock-data-loader.js`)

Utility for loading and using collected execution data in tests.

**Features:**
- Loads execution data with caching
- Creates Jest mock functions based on real execution patterns
- Provides execution statistics and scenario analysis
- Supports both sequential and pattern-based mocking

**Usage:**
```javascript
const mockDataLoader = require('./mock-data-loader');

// Create mock function based on collected data
const mockFn = mockDataLoader.createMockFunction('functionName');

// Get execution statistics
const stats = mockDataLoader.getExecutionStats('functionName');

// Check if data exists
if (mockDataLoader.hasExecutionData('functionName')) {
  // Use collected data
}
```

## How It Works

### 1. Data Collection (Dev Mode)

When the application runs with the `--dev` flag:

1. **Function Instrumentation**: Key functions are wrapped with execution collectors
2. **Parameter Capture**: Function parameters are captured and sanitized
3. **Result Tracking**: Return values and errors are recorded
4. **Context Information**: Execution time, environment details, and custom context are saved
5. **Data Storage**: All data is saved to `tests/mock-data/` as JSON files

### 2. Mock Generation

The mock generation process:

1. **Data Loading**: Execution data is loaded from JSON files
2. **Scenario Grouping**: Similar executions are grouped by parameter patterns
3. **Mock Configuration**: Jest mock functions are configured based on real behavior
4. **File Generation**: Complete mock files are generated with setup utilities

### 3. Test Integration

Tests can use the collected data in several ways:

1. **Automatic Mocking**: Use `mockDataLoader.createMockFunction()` for automatic setup
2. **Scenario Testing**: Test specific scenarios based on collected data
3. **Behavior Validation**: Verify that test behavior matches real execution patterns
4. **Fallback Handling**: Gracefully handle cases where no execution data exists

## File Structure

```
tests/
├── mock-data/                 # Collected execution data (JSON files)
│   ├── functionName.json      # Execution data for specific function
│   └── summary.json           # Summary of all collected data
├── generated-mocks/           # Auto-generated mock files
│   └── functionName.mock.js   # Generated Jest mock for function
├── mock-data-generator.js     # Mock generation utilities
└── mock-data-loader.js        # Test integration utilities

src/
├── execution-collector.js     # Main collection system
└── [instrumented files]       # Files with execution collection

scripts/
└── generate-mocks.js          # Script to run app and generate mocks
```

## Usage Examples

### Adding Execution Collection to a Function

```javascript
// In your source file
const executionCollector = require('./execution-collector');

class MyClass {
  async myFunction(param1, param2) {
    const startTime = Date.now();
    let result = null;
    let error = null;
    
    try {
      // Your function logic here
      result = await someOperation(param1, param2);
      return result;
    } catch (err) {
      error = err;
      throw err;
    } finally {
      executionCollector.collect(
        'myFunction',
        { param1, param2 },
        result,
        error,
        { 
          executionTime: Date.now() - startTime,
          customContext: 'additional info'
        }
      );
    }
  }
}
```

### Using Collected Data in Tests

```javascript
// In your test file
const mockDataLoader = require('./mock-data-loader');

describe('MyClass', () => {
  test('should use collected execution data when available', () => {
    if (mockDataLoader.hasExecutionData('myFunction')) {
      const stats = mockDataLoader.getExecutionStats('myFunction');
      console.log('Using collected data:', stats);
      
      const mockFn = mockDataLoader.createMockFunction('myFunction');
      // Use mockFn in your test
    } else {
      // Fallback to default test behavior
    }
  });
});
```

## Scripts

### Generate Mock Data

```bash
# Run application in dev mode and generate mocks
npm run test:generate-mocks

# Run tests with fresh mock data
npm run test:with-mocks
```

### Manual Mock Generation

```bash
# Generate mocks from existing execution data
node -e "
const MockDataGenerator = require('./tests/mock-data-generator');
new MockDataGenerator().generateAllMocks();
"
```

## Benefits

1. **Realistic Test Data**: Mocks based on actual application behavior
2. **Automatic Generation**: No manual mock creation required
3. **Behavior Validation**: Tests can verify they match real execution patterns
4. **Scenario Coverage**: Automatically captures different execution scenarios
5. **Maintenance Reduction**: Mocks update automatically as code changes

## Best Practices

1. **Run in Dev Mode**: Always collect data during development and testing
2. **Exercise All Paths**: Ensure comprehensive code coverage during data collection
3. **Review Generated Mocks**: Verify that generated mocks make sense
4. **Version Control**: Consider committing collected data for team consistency
5. **Regular Updates**: Regenerate mocks when significant changes are made

## Troubleshooting

### No Execution Data Collected

- Ensure the application is running with `--dev` flag
- Check that functions are properly instrumented
- Verify the application runs long enough to execute target functions

### Mock Generation Fails

- Check that `tests/mock-data/` directory exists and contains JSON files
- Verify JSON files are valid and not corrupted
- Ensure sufficient permissions for file operations

### Tests Not Using Collected Data

- Verify `mockDataLoader.hasExecutionData()` returns true
- Check file paths and naming conventions
- Ensure test files are importing the mock data loader correctly
