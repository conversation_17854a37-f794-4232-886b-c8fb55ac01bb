const fs = require('fs');
const path = require('path');

/**
 * MockDataLoader - Utility to load and use collected execution data in tests
 */
class MockDataLoader {
  constructor() {
    this.mockDataDir = path.join(__dirname, 'mock-data');
    this.generatedMocksDir = path.join(__dirname, 'generated-mocks');
    this.cache = new Map();
  }

  /**
   * Load execution data for a specific function
   * @param {string} functionName - Name of the function
   * @returns {Array|null} Execution data or null if not found
   */
  loadExecutionData(functionName) {
    if (this.cache.has(functionName)) {
      return this.cache.get(functionName);
    }

    const filename = `${functionName.replace(/[^a-zA-Z0-9]/g, '_')}.json`;
    const filepath = path.join(this.mockDataDir, filename);
    
    try {
      if (fs.existsSync(filepath)) {
        const data = JSON.parse(fs.readFileSync(filepath, 'utf8'));
        this.cache.set(functionName, data);
        return data;
      }
    } catch (error) {
      console.warn(`Failed to load execution data for ${functionName}:`, error.message);
    }
    
    this.cache.set(functionName, null);
    return null;
  }

  /**
   * Create a Jest mock function based on collected execution data
   * @param {string} functionName - Name of the function
   * @param {Object} options - Mock options
   * @returns {Function} Jest mock function
   */
  createMockFunction(functionName, options = {}) {
    const executionData = this.loadExecutionData(functionName);
    const mockFn = jest.fn();

    if (!executionData || executionData.length === 0) {
      // No execution data available, use default behavior
      if (options.defaultReturnValue !== undefined) {
        mockFn.mockReturnValue(options.defaultReturnValue);
      }
      if (options.defaultResolvedValue !== undefined) {
        mockFn.mockResolvedValue(options.defaultResolvedValue);
      }
      return mockFn;
    }

    // Configure mock based on execution data
    this.configureMockFromExecutionData(mockFn, executionData, options);
    
    return mockFn;
  }

  /**
   * Configure a Jest mock function based on execution data
   * @param {Function} mockFn - Jest mock function
   * @param {Array} executionData - Execution data
   * @param {Object} options - Configuration options
   */
  configureMockFromExecutionData(mockFn, executionData, options = {}) {
    // Group executions by scenario
    const scenarios = this.groupExecutionsByScenario(executionData);
    
    if (options.useSequentialResponses) {
      // Use responses in sequence
      scenarios.forEach(scenario => {
        scenario.results.forEach(result => {
          if (result.error) {
            mockFn.mockRejectedValueOnce(new Error(result.error.message));
          } else if (this.isPromiseResult(result.result)) {
            mockFn.mockResolvedValueOnce(result.result);
          } else {
            mockFn.mockReturnValueOnce(result.result);
          }
        });
      });
    } else {
      // Use most common response as default
      const mostCommonResult = this.getMostCommonResult(executionData);
      if (mostCommonResult) {
        if (mostCommonResult.error) {
          mockFn.mockRejectedValue(new Error(mostCommonResult.error.message));
        } else if (this.isPromiseResult(mostCommonResult.result)) {
          mockFn.mockResolvedValue(mostCommonResult.result);
        } else {
          mockFn.mockReturnValue(mostCommonResult.result);
        }
      }
    }

    // Add custom implementation if provided
    if (options.customImplementation) {
      mockFn.mockImplementation(options.customImplementation);
    }
  }

  /**
   * Group execution data by similar scenarios
   * @param {Array} executionData - Execution data
   * @returns {Array} Grouped scenarios
   */
  groupExecutionsByScenario(executionData) {
    const scenarioMap = new Map();
    
    executionData.forEach(execution => {
      const key = this.generateScenarioKey(execution.params);
      
      if (!scenarioMap.has(key)) {
        scenarioMap.set(key, {
          key,
          params: execution.params,
          results: []
        });
      }
      
      scenarioMap.get(key).results.push({
        result: execution.result,
        error: execution.error,
        executionTime: execution.context?.executionTime || 0,
        timestamp: execution.timestamp
      });
    });
    
    return Array.from(scenarioMap.values());
  }

  /**
   * Generate a key for grouping similar execution scenarios
   * @param {Object} params - Function parameters
   * @returns {string} Scenario key
   */
  generateScenarioKey(params) {
    if (!params || typeof params !== 'object') {
      return 'default';
    }

    const keyParts = [];
    
    if (params.args && Array.isArray(params.args)) {
      keyParts.push(`args_${params.args.length}`);
      params.args.forEach((arg, index) => {
        keyParts.push(`arg${index}_${typeof arg}`);
      });
    }

    return keyParts.length > 0 ? keyParts.join('_') : 'default';
  }

  /**
   * Get the most common result from execution data
   * @param {Array} executionData - Execution data
   * @returns {Object|null} Most common result
   */
  getMostCommonResult(executionData) {
    if (!executionData || executionData.length === 0) {
      return null;
    }

    // Count occurrences of each result
    const resultCounts = new Map();
    
    executionData.forEach(execution => {
      const key = JSON.stringify({
        result: execution.result,
        error: execution.error ? execution.error.message : null
      });
      
      resultCounts.set(key, (resultCounts.get(key) || 0) + 1);
    });

    // Find most common result
    let mostCommonKey = null;
    let maxCount = 0;
    
    for (const [key, count] of resultCounts.entries()) {
      if (count > maxCount) {
        maxCount = count;
        mostCommonKey = key;
      }
    }

    if (mostCommonKey) {
      const parsed = JSON.parse(mostCommonKey);
      return {
        result: parsed.result,
        error: parsed.error ? { message: parsed.error } : null
      };
    }

    return executionData[0]; // Fallback to first result
  }

  /**
   * Check if a result appears to be from a Promise
   * @param {*} result - Result to check
   * @returns {boolean} True if result appears to be from a Promise
   */
  isPromiseResult(result) {
    // This is a heuristic - in practice, we might need better detection
    return result && typeof result === 'object' && 
           (result.success !== undefined || result.error !== undefined);
  }

  /**
   * Create mock data for a test scenario
   * @param {string} functionName - Name of the function
   * @param {string} scenario - Scenario name
   * @returns {Object} Mock data for the scenario
   */
  getMockDataForScenario(functionName, scenario = 'default') {
    const executionData = this.loadExecutionData(functionName);
    if (!executionData) {
      return null;
    }

    const scenarios = this.groupExecutionsByScenario(executionData);
    const targetScenario = scenarios.find(s => s.key === scenario) || scenarios[0];
    
    if (!targetScenario) {
      return null;
    }

    return {
      params: targetScenario.params,
      results: targetScenario.results,
      executionCount: targetScenario.results.length
    };
  }

  /**
   * Get execution statistics for a function
   * @param {string} functionName - Name of the function
   * @returns {Object|null} Execution statistics
   */
  getExecutionStats(functionName) {
    const executionData = this.loadExecutionData(functionName);
    if (!executionData) {
      return null;
    }

    const stats = {
      totalExecutions: executionData.length,
      successfulExecutions: 0,
      failedExecutions: 0,
      averageExecutionTime: 0,
      scenarios: 0
    };

    let totalExecutionTime = 0;
    
    executionData.forEach(execution => {
      if (execution.error) {
        stats.failedExecutions++;
      } else {
        stats.successfulExecutions++;
      }
      
      if (execution.context?.executionTime) {
        totalExecutionTime += execution.context.executionTime;
      }
    });

    stats.averageExecutionTime = totalExecutionTime / executionData.length;
    stats.scenarios = this.groupExecutionsByScenario(executionData).length;

    return stats;
  }

  /**
   * Clear the cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Check if execution data exists for a function
   * @param {string} functionName - Name of the function
   * @returns {boolean} True if data exists
   */
  hasExecutionData(functionName) {
    const filename = `${functionName.replace(/[^a-zA-Z0-9]/g, '_')}.json`;
    const filepath = path.join(this.mockDataDir, filename);
    return fs.existsSync(filepath);
  }
}

// Create singleton instance
const mockDataLoader = new MockDataLoader();

module.exports = mockDataLoader;
