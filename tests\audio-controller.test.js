const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');
const mockDataLoader = require('./mock-data-loader');

// Mock electron module
const mockWebContents = {
  setAudioMuted: jest.fn(),
  isDestroyed: jest.fn(() => false),
  isAudioMuted: jest.fn(() => true)
};

const mockExecAsync = jest.fn();

// Mock child_process
jest.mock('child_process', () => ({
  exec: jest.fn()
}));

// Mock util.promisify
jest.mock('util', () => ({
  promisify: jest.fn(() => mockExecAsync)
}));

// Import the AudioController after mocking
const AudioController = require('../src/audio-controller');

describe('AudioController', () => {
  let audioController;

  beforeEach(() => {
    audioController = new AudioController();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('constructor', () => {
    test('should initialize with correct default values', () => {
      expect(audioController.isMuted).toBe(false);
      expect(audioController.webContents).toEqual([]);
    });
  });

  describe('registerWebContents', () => {
    test('should add web contents to the array', () => {
      audioController.registerWebContents(mockWebContents);
      expect(audioController.webContents).toContain(mockWebContents);
    });

    test('should not add duplicate web contents', () => {
      audioController.registerWebContents(mockWebContents);
      audioController.registerWebContents(mockWebContents);
      expect(audioController.webContents.length).toBe(1);
    });

    test('should not add null or undefined web contents', () => {
      audioController.registerWebContents(null);
      audioController.registerWebContents(undefined);
      expect(audioController.webContents.length).toBe(0);
    });
  });

  describe('unregisterWebContents', () => {
    test('should remove web contents from the array', () => {
      audioController.registerWebContents(mockWebContents);
      audioController.unregisterWebContents(mockWebContents);
      expect(audioController.webContents).not.toContain(mockWebContents);
    });

    test('should handle removing non-existent web contents gracefully', () => {
      const initialLength = audioController.webContents.length;
      audioController.unregisterWebContents(mockWebContents);
      expect(audioController.webContents.length).toBe(initialLength);
    });
  });

  describe('toggleMute', () => {
    test('should toggle mute state from false to true', async () => {
      audioController.registerWebContents(mockWebContents);
      
      const result = await audioController.toggleMute();
      
      expect(audioController.isMuted).toBe(true);
      expect(result).toBe(true);
      expect(mockWebContents.setAudioMuted).toHaveBeenCalledWith(true);
    });

    test('should toggle mute state from true to false', async () => {
      audioController.isMuted = true;
      audioController.registerWebContents(mockWebContents);
      
      const result = await audioController.toggleMute();
      
      expect(audioController.isMuted).toBe(false);
      expect(result).toBe(false);
      expect(mockWebContents.setAudioMuted).toHaveBeenCalledWith(false);
    });

    test('should handle destroyed web contents gracefully', async () => {
      const destroyedWebContents = {
        setAudioMuted: jest.fn(),
        isDestroyed: jest.fn(() => true)
      };
      
      audioController.registerWebContents(destroyedWebContents);
      
      const result = await audioController.toggleMute();
      
      expect(result).toBe(true);
      expect(destroyedWebContents.setAudioMuted).not.toHaveBeenCalled();
    });

    test('should continue with other web contents if one fails', async () => {
      const failingWebContents = {
        setAudioMuted: jest.fn(() => { throw new Error('Test error'); }),
        isDestroyed: jest.fn(() => false)
      };
      
      audioController.registerWebContents(failingWebContents);
      audioController.registerWebContents(mockWebContents);
      
      const result = await audioController.toggleMute();
      
      expect(result).toBe(true);
      expect(mockWebContents.setAudioMuted).toHaveBeenCalledWith(true);
    });

    test('should handle individual web content errors gracefully', async () => {
      // Create a mock that throws an error
      const errorWebContents = {
        setAudioMuted: jest.fn(() => {
          throw new Error('Test error');
        }),
        isDestroyed: jest.fn(() => false),
        isAudioMuted: jest.fn(() => false)
      };

      audioController.registerWebContents(errorWebContents);

      // The method should not throw, but handle the error gracefully
      const result = await audioController.toggleMute();

      // Should still return the new mute state even if individual web contents fail
      expect(result).toBe(true);
      expect(audioController.isMuted).toBe(true);
    });
  });

  describe('setMute', () => {
    test('should not change state if already in desired state', async () => {
      audioController.isMuted = true;
      
      const result = await audioController.setMute(true);
      
      expect(result).toBe(true);
      expect(mockWebContents.setAudioMuted).not.toHaveBeenCalled();
    });

    test('should toggle if not in desired state', async () => {
      audioController.registerWebContents(mockWebContents);
      audioController.isMuted = false;
      
      const result = await audioController.setMute(true);
      
      expect(result).toBe(true);
      expect(mockWebContents.setAudioMuted).toHaveBeenCalledWith(true);
    });
  });

  describe('execution data integration', () => {
    test('should use collected execution data when available', async () => {
      // Check if we have collected execution data for toggleMute
      if (mockDataLoader.hasExecutionData('toggleMute')) {
        const stats = mockDataLoader.getExecutionStats('toggleMute');
        console.log('Using collected execution data for toggleMute:', stats);

        // Use the collected data to inform our test expectations
        const mockData = mockDataLoader.getMockDataForScenario('toggleMute');
        if (mockData && mockData.results.length > 0) {
          const firstResult = mockData.results[0];

          // Test should reflect the actual behavior observed during execution
          audioController.registerWebContents(mockWebContents);
          const result = await audioController.toggleMute();

          // Verify the result matches the pattern from collected data
          expect(typeof result).toBe('boolean');
          expect(mockWebContents.setAudioMuted).toHaveBeenCalled();

          // Log the comparison for debugging
          console.log('Expected from collected data:', firstResult.result);
          console.log('Actual test result:', result);
        }
      } else {
        console.log('No collected execution data for toggleMute, using default test');
        // Fall back to default test behavior
        audioController.registerWebContents(mockWebContents);
        const result = await audioController.toggleMute();
        expect(typeof result).toBe('boolean');
      }
    });

    test('should validate execution patterns match collected data', () => {
      // This test validates that our mock behavior matches real execution patterns
      const functionNames = ['toggleMute', 'setMute', 'registerWebContents'];

      functionNames.forEach(functionName => {
        if (mockDataLoader.hasExecutionData(functionName)) {
          const stats = mockDataLoader.getExecutionStats(functionName);
          console.log(`Execution stats for ${functionName}:`, stats);

          // Validate that we have reasonable execution data
          expect(stats.totalExecutions).toBeGreaterThan(0);
          expect(stats.scenarios).toBeGreaterThan(0);
        }
      });
    });
  });
});
