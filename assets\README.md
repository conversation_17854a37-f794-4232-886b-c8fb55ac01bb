# Assets Directory

This directory contains the application icons and other assets.

## Required Icons

- `icon.png` - Main application icon (512x512 PNG)
- `icon.ico` - Windows icon file
- `icon.icns` - macOS icon file

## Icon Requirements

### Windows (.ico)
- Multiple sizes: 16x16, 32x32, 48x48, 256x256
- Format: ICO

### macOS (.icns)
- Multiple sizes: 16x16, 32x32, 128x128, 256x256, 512x512
- Format: ICNS

### Linux (.png)
- Size: 512x512
- Format: PNG

## Creating Icons

You can use online tools or software like:
- https://convertio.co/png-ico/ (PNG to ICO)
- https://iconverticons.com/online/ (PNG to ICNS)
- Or use electron-icon-builder package

## Placeholder Icons

For development, you can use simple placeholder icons or the default Electron icon.
