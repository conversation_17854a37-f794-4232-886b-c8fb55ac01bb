const SpotifyInteractionReplayer = require('../src/spotify-interaction-replayer');

describe('SpotifyInteractionReplayer', () => {
  let replayer;
  let mockWebContents;

  beforeEach(() => {
    replayer = new SpotifyInteractionReplayer();
    mockWebContents = {
      executeJavaScript: jest.fn(),
      loadURL: jest.fn(),
      getURL: jest.fn(() => 'https://accounts.spotify.com/login')
    };
    
    jest.clearAllMocks();
  });

  describe('Replay lifecycle', () => {
    test('should handle empty interactions array', async () => {
      const result = await replayer.replayInteractions([], mockWebContents);
      
      expect(result).toBe(false);
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should handle null interactions', async () => {
      const result = await replayer.replayInteractions(null, mockWebContents);
      
      expect(result).toBe(false);
    });

    test('should inject replay script before replaying', async () => {
      const interactions = [
        { type: 'click', selector: '#test', timestamp: 0 }
      ];
      
      mockWebContents.executeJavaScript.mockResolvedValue(true);
      
      await replayer.replayInteractions(interactions, mockWebContents);
      
      // First call should be script injection
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('window.spotifyReplayUtils')
      );
    });

    test('should stop replay when requested', async () => {
      const interactions = [
        { type: 'click', selector: '#test1', timestamp: 0 },
        { type: 'click', selector: '#test2', timestamp: 1000 }
      ];
      
      mockWebContents.executeJavaScript.mockResolvedValue(true);
      
      // Start replay and immediately stop it
      const replayPromise = replayer.replayInteractions(interactions, mockWebContents);
      replayer.stopReplay();
      
      const result = await replayPromise;
      expect(result).toBe(false);
    });
  });

  describe('Interaction execution', () => {
    beforeEach(() => {
      // Mock script injection and execution
      mockWebContents.executeJavaScript.mockImplementation((script) => {
        if (script.includes('window.spotifyReplayUtils')) {
          return Promise.resolve();
        }
        if (script.includes('findElement')) {
          return Promise.resolve(true);
        }
        if (script.includes('element.value =')) {
          return Promise.resolve(true);
        }
        if (script.includes('form.submit()')) {
          return Promise.resolve(true);
        }
        if (script.includes('readyState')) {
          return Promise.resolve({ readyState: 'complete', hasSpinner: false });
        }
        return Promise.resolve(true);
      });
    });

    test('should execute click interactions', async () => {
      const interaction = {
        type: 'click',
        selector: '#login-button',
        timestamp: 0
      };
      
      const result = await replayer.executeClick(interaction, mockWebContents);
      
      expect(result).toBe(true);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('findElement')
      );
    });

    test('should execute input interactions', async () => {
      const interaction = {
        type: 'input',
        selector: '#username',
        value: '<EMAIL>',
        timestamp: 0
      };
      
      const result = await replayer.executeInput(interaction, mockWebContents);
      
      expect(result).toBe(true);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('element.value = ')
      );
    });

    test('should execute submit interactions', async () => {
      const interaction = {
        type: 'submit',
        selector: '#login-form',
        timestamp: 0
      };
      
      const result = await replayer.executeSubmit(interaction, mockWebContents);
      
      expect(result).toBe(true);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('form.submit()')
      );
    });

    test('should execute navigation interactions', async () => {
      const interaction = {
        type: 'navigation',
        toUrl: 'https://open.spotify.com',
        timestamp: 0
      };
      
      const result = await replayer.executeNavigation(interaction, mockWebContents);
      
      expect(result).toBe(true);
      expect(mockWebContents.loadURL).toHaveBeenCalledWith('https://open.spotify.com');
    });

    test('should handle unknown interaction types', async () => {
      const interaction = {
        type: 'unknown',
        selector: '#test',
        timestamp: 0
      };
      
      const result = await replayer.executeInteraction(interaction, mockWebContents);
      
      expect(result).toBe(false);
    });

    test('should handle execution errors gracefully', async () => {
      // Reset mock to reject for this test
      mockWebContents.executeJavaScript.mockReset();
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('Execution failed'));

      const interaction = {
        type: 'click',
        selector: '#test',
        timestamp: 0
      };

      const result = await replayer.executeClick(interaction, mockWebContents);

      expect(result).toBe(false);
    });
  });

  describe('Enhanced input handling', () => {
    beforeEach(() => {
      mockWebContents.executeJavaScript.mockImplementation((script) => {
        if (script.includes('window.spotifyReplayUtils')) {
          return Promise.resolve();
        }
        return Promise.resolve(true);
      });
    });

    test('should handle checkbox inputs', async () => {
      const interaction = {
        type: 'input',
        selector: '#remember-me',
        value: 'true',
        inputType: 'checkbox',
        timestamp: 0
      };
      
      await replayer.executeInput(interaction, mockWebContents);
      
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('checkbox')
      );
    });

    test('should handle select elements', async () => {
      const interaction = {
        type: 'input',
        selector: '#country',
        value: 'US',
        timestamp: 0
      };
      
      await replayer.executeInput(interaction, mockWebContents);
      
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('select')
      );
    });

    test('should handle special characters in input values', async () => {
      const interaction = {
        type: 'input',
        selector: '#password',
        value: "test'password\"with&special<chars>",
        timestamp: 0
      };
      
      await replayer.executeInput(interaction, mockWebContents);
      
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining("\\'")
      );
    });
  });

  describe('Page load waiting', () => {
    test('should wait for page to complete loading', async () => {
      mockWebContents.executeJavaScript
        .mockResolvedValueOnce({ readyState: 'loading', hasSpinner: true })
        .mockResolvedValueOnce({ readyState: 'complete', hasSpinner: false });
      
      const startTime = Date.now();
      await replayer.waitForPageLoad(mockWebContents, 5000);
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeGreaterThan(500); // Should wait at least for the delay
      expect(mockWebContents.executeJavaScript).toHaveBeenCalled();
    });

    test('should timeout if page never loads', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue({ 
        readyState: 'loading', 
        hasSpinner: true 
      });
      
      const startTime = Date.now();
      await replayer.waitForPageLoad(mockWebContents, 1000);
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeGreaterThanOrEqual(1000);
    });

    test('should handle webContents errors during page load wait', async () => {
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('WebContents error'));
      
      // Should not throw
      await expect(replayer.waitForPageLoad(mockWebContents, 1000)).resolves.toBeUndefined();
    });
  });

  describe('Authentication step detection', () => {
    test('should detect login page', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue('login_page');
      
      const step = await replayer.detectAuthenticationStep(mockWebContents);
      
      expect(step).toBe('login_page');
    });

    test('should detect two-factor authentication', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue('two_factor');
      
      const step = await replayer.detectAuthenticationStep(mockWebContents);
      
      expect(step).toBe('two_factor');
    });

    test('should detect logged-in state', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue('logged_in');
      
      const step = await replayer.detectAuthenticationStep(mockWebContents);
      
      expect(step).toBe('logged_in');
    });

    test('should handle detection errors', async () => {
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('Detection failed'));
      
      const step = await replayer.detectAuthenticationStep(mockWebContents);
      
      expect(step).toBe('unknown');
    });
  });

  describe('Progress tracking', () => {
    test('should track replay progress', () => {
      expect(replayer.getProgress().isReplaying).toBe(false);
      
      replayer.isReplaying = true;
      replayer.currentInteractionIndex = 5;
      replayer.replayStartTime = Date.now() - 1000;
      
      const progress = replayer.getProgress();
      expect(progress.isReplaying).toBe(true);
      expect(progress.currentIndex).toBe(5);
      expect(progress.elapsedTime).toBeGreaterThan(900);
    });
  });

  describe('Utility methods', () => {
    test('should delay execution', async () => {
      const startTime = Date.now();
      await replayer.delay(100);
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeGreaterThanOrEqual(100);
    });

    test('should stop replay', () => {
      replayer.isReplaying = true;
      replayer.stopReplay();
      
      expect(replayer.isReplaying).toBe(false);
    });
  });
});
