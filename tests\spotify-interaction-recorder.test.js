const fs = require('fs');
const path = require('path');
const { app } = require('electron');
const SpotifyInteractionRecorder = require('../src/spotify-interaction-recorder');

// Mock electron modules
jest.mock('electron', () => ({
  app: {
    getPath: jest.fn(() => '/mock/user/data')
  }
}));

// Mock fs
jest.mock('fs', () => ({
  existsSync: jest.fn(),
  promises: {
    writeFile: jest.fn(),
    readFile: jest.fn(),
    unlink: jest.fn()
  }
}));

describe('SpotifyInteractionRecorder', () => {
  let recorder;
  let mockWebContents;

  beforeEach(() => {
    recorder = new SpotifyInteractionRecorder();
    mockWebContents = {
      executeJavaScript: jest.fn()
    };
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Recording lifecycle', () => {
    test('should start recording correctly', () => {
      expect(recorder.isRecording).toBe(false);
      
      recorder.startRecording();
      
      expect(recorder.isRecording).toBe(true);
      expect(recorder.recordedInteractions).toEqual([]);
      expect(recorder.recordingStartTime).toBeDefined();
    });

    test('should stop recording and save interactions', async () => {
      recorder.startRecording();
      recorder.recordInteraction({ type: 'click', selector: '#test' });
      
      await recorder.stopRecording();
      
      expect(recorder.isRecording).toBe(false);
      expect(fs.promises.writeFile).toHaveBeenCalled();
    });

    test('should not stop recording if not currently recording', async () => {
      expect(recorder.isRecording).toBe(false);
      
      await recorder.stopRecording();
      
      expect(fs.promises.writeFile).not.toHaveBeenCalled();
    });
  });

  describe('Interaction recording', () => {
    test('should record interaction when recording is active', () => {
      recorder.startRecording();
      const interaction = {
        type: 'click',
        selector: '#login-button',
        tagName: 'button'
      };
      
      recorder.recordInteraction(interaction);
      
      expect(recorder.recordedInteractions).toHaveLength(1);
      expect(recorder.recordedInteractions[0]).toMatchObject(interaction);
      expect(recorder.recordedInteractions[0].timestamp).toBeDefined();
      expect(recorder.recordedInteractions[0].id).toBeDefined();
    });

    test('should not record interaction when recording is inactive', () => {
      const interaction = { type: 'click', selector: '#test' };
      
      recorder.recordInteraction(interaction);
      
      expect(recorder.recordedInteractions).toHaveLength(0);
    });

    test('should generate unique interaction IDs', () => {
      recorder.startRecording();
      
      recorder.recordInteraction({ type: 'click', selector: '#test1' });
      recorder.recordInteraction({ type: 'click', selector: '#test2' });
      
      const ids = recorder.recordedInteractions.map(i => i.id);
      expect(ids[0]).not.toBe(ids[1]);
      expect(ids[0]).toMatch(/^interaction_\d+_[a-z0-9]+$/);
    });
  });

  describe('File operations', () => {
    test('should save recording data correctly', async () => {
      recorder.startRecording();
      recorder.recordInteraction({ type: 'click', selector: '#test' });
      
      await recorder.saveRecording();
      
      expect(fs.promises.writeFile).toHaveBeenCalledWith(
        '/mock/user/data/spotify-login-recording.json',
        expect.stringContaining('"version": "1.0"'),
        'utf8'
      );
    });

    test('should load recording data correctly', async () => {
      const mockRecording = {
        version: '1.0',
        totalInteractions: 2,
        interactions: [
          { type: 'click', selector: '#test1' },
          { type: 'input', selector: '#test2' }
        ]
      };
      
      fs.existsSync.mockReturnValue(true);
      fs.promises.readFile.mockResolvedValue(JSON.stringify(mockRecording));
      
      const result = await recorder.loadRecording();
      
      expect(result).toEqual(mockRecording);
      expect(fs.promises.readFile).toHaveBeenCalledWith(
        '/mock/user/data/spotify-login-recording.json',
        'utf8'
      );
    });

    test('should return null when loading non-existent recording', async () => {
      fs.existsSync.mockReturnValue(false);
      
      const result = await recorder.loadRecording();
      
      expect(result).toBeNull();
      expect(fs.promises.readFile).not.toHaveBeenCalled();
    });

    test('should handle file read errors gracefully', async () => {
      fs.existsSync.mockReturnValue(true);
      fs.promises.readFile.mockRejectedValue(new Error('Read error'));
      
      const result = await recorder.loadRecording();
      
      expect(result).toBeNull();
    });

    test('should check if recording exists', () => {
      fs.existsSync.mockReturnValue(true);
      expect(recorder.hasRecording()).toBe(true);
      
      fs.existsSync.mockReturnValue(false);
      expect(recorder.hasRecording()).toBe(false);
    });

    test('should delete recording file', async () => {
      fs.existsSync.mockReturnValue(true);
      
      await recorder.deleteRecording();
      
      expect(fs.promises.unlink).toHaveBeenCalledWith(
        '/mock/user/data/spotify-login-recording.json'
      );
    });
  });

  describe('Script injection', () => {
    test('should inject recording script when recording is active', async () => {
      recorder.startRecording();
      
      await recorder.injectRecordingScript(mockWebContents);
      
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('window.spotifyRecorderInjected')
      );
    });

    test('should not inject script when recording is inactive', async () => {
      await recorder.injectRecordingScript(mockWebContents);
      
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should handle script injection errors gracefully', async () => {
      recorder.startRecording();
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('Injection failed'));
      
      // Should not throw
      await expect(recorder.injectRecordingScript(mockWebContents)).resolves.toBeUndefined();
    });
  });

  describe('Interaction polling', () => {
    test('should poll recorded interactions when recording is active', async () => {
      recorder.startRecording();
      const mockInteractions = [
        { type: 'click', selector: '#test1' },
        { type: 'input', selector: '#test2' }
      ];
      
      mockWebContents.executeJavaScript.mockResolvedValue(mockInteractions);
      
      await recorder.pollRecordedInteractions(mockWebContents);
      
      expect(recorder.recordedInteractions).toHaveLength(2);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('window.spotifyRecordedInteractions')
      );
    });

    test('should not poll when recording is inactive', async () => {
      await recorder.pollRecordedInteractions(mockWebContents);
      
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should handle polling errors gracefully', async () => {
      recorder.startRecording();
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('Polling failed'));
      
      // Should not throw
      await expect(recorder.pollRecordedInteractions(mockWebContents)).resolves.toBeUndefined();
    });
  });

  describe('Edge cases', () => {
    test('should handle save errors gracefully', async () => {
      recorder.startRecording();
      fs.promises.writeFile.mockRejectedValue(new Error('Write error'));
      
      // Should not throw
      await expect(recorder.saveRecording()).resolves.toBeUndefined();
    });

    test('should handle delete errors gracefully', async () => {
      fs.existsSync.mockReturnValue(true);
      fs.promises.unlink.mockRejectedValue(new Error('Delete error'));
      
      // Should not throw
      await expect(recorder.deleteRecording()).resolves.toBeUndefined();
    });

    test('should handle invalid JSON when loading', async () => {
      fs.existsSync.mockReturnValue(true);
      fs.promises.readFile.mockResolvedValue('invalid json');
      
      const result = await recorder.loadRecording();
      
      expect(result).toBeNull();
    });
  });
});
