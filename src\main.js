const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Brows<PERSON><PERSON><PERSON><PERSON>, <PERSON>ray, Menu, ipc<PERSON>ain, screen, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const Store = require('electron-store');
const AutoLaunch = require('auto-launch');
const PlaylistManager = require('./playlist-manager');
const AudioController = require('./audio-controller');
const YouTubeAdSkipper = require('./youtube-ad-skipper');
const VideoProgressMonitor = require('./video-progress-monitor');
const executionCollector = require('./execution-collector');
const SpotifyInteractionRecorder = require('./spotify-interaction-recorder');
const SpotifyInteractionReplayer = require('./spotify-interaction-replayer');

// Initialize store for persistent data
const store = new Store();

// Global references
let mainWindow = null;
let tray = null;
let youtubeView = null;
let spotifyView = null;
let isMinimized = false;
let isMuted = false;
let playlistManager = null;
let audioController = null;
let youtubeAdSkipper = null;
let videoProgressMonitor = null;
let appInstance = null; // Singleton instance

// Auto-launch setup
const autoLauncher = new AutoLaunch({
  name: 'YSViewer',
  path: app.getPath('exe')
});

class YSViewerApp {
  constructor() {
    if (appInstance) {
      return appInstance;
    }
    appInstance = this;
    this.initializeApp();
  }

  static getInstance() {
    if (!appInstance) {
      appInstance = new YSViewerApp();
    }
    return appInstance;
  }

  async initializeApp() {
    // Check if running in development mode and clear session data
    if (process.argv.includes('--dev')) {
      console.log('[Dev Mode] Clearing Spotify session data for fresh login');
      await this.clearSpotifySessionData();
    }

    // Initialize managers
    playlistManager = new PlaylistManager();
    audioController = new AudioController();
    youtubeAdSkipper = new YouTubeAdSkipper();
    videoProgressMonitor = new VideoProgressMonitor();
    this.spotifyRecorder = new SpotifyInteractionRecorder();
    this.spotifyReplayer = new SpotifyInteractionReplayer();

    // Set mute state but don't apply yet (no web contents registered)
    isMuted = true;
    console.log('Application will start muted by default');

    // Setup auto-launch
    await this.setupAutoLaunch();

    // Create main window
    this.createMainWindow();

    // Create system tray
    this.createSystemTray();

    // Setup browser views
    this.setupBrowserViews();

    // Now apply mute state after web contents are registered
    await audioController.setMute(true);
    console.log('Application started muted by default - mute applied to web contents');

    // Start playlist management
    await this.startPlaylistManagement();
  }

  async setupAutoLaunch() {
    try {
      const isEnabled = await autoLauncher.isEnabled();
      if (!isEnabled) {
        await autoLauncher.enable();
        console.log('Auto-launch enabled');
      }
    } catch (error) {
      console.error('Failed to setup auto-launch:', error);
    }
  }

  createMainWindow() {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    mainWindow = new BrowserWindow({
      width: Math.min(1400, width - 100),
      height: Math.min(800, height - 100),
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        webSecurity: false
      },
      icon: path.join(__dirname, '../assets/icon.png'),
      show: false,
      frame: true,
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default'
    });

    // Load the main HTML file
    mainWindow.loadFile(path.join(__dirname, 'electron-index.html'));

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
      mainWindow.show();
      if (process.argv.includes('--dev')) {
        mainWindow.webContents.openDevTools();
      }
    });

    // Handle window minimize - system minimize button should minimize to tray
    mainWindow.on('minimize', (event) => {
      event.preventDefault();
      this.minimizeToTray();
    });
  }

  createSystemTray() {
    try {
      const { nativeImage } = require('electron');

      // Try to use the app icon first, fallback to generated icon
      let trayIcon;
      try {
        // Try to load the app icon for tray
        const iconPath = path.join(__dirname, '../assets/icon.png');
        trayIcon = nativeImage.createFromPath(iconPath);

        // If icon is empty or invalid, create a simple one
        if (trayIcon.isEmpty()) {
          throw new Error('Icon file not found or invalid');
        }

        // Resize for tray (Windows typically uses 16x16)
        trayIcon = trayIcon.resize({ width: 16, height: 16 });
      } catch (iconError) {
        console.log('Using fallback tray icon:', iconError.message);

        // Create a simple 16x16 green square icon as fallback
        const size = 16;
        const buffer = Buffer.alloc(size * size * 4);

        // Fill with green color
        for (let i = 0; i < buffer.length; i += 4) {
          buffer[i] = 76;     // R
          buffer[i + 1] = 175; // G
          buffer[i + 2] = 80;  // B
          buffer[i + 3] = 255; // A
        }

        trayIcon = nativeImage.createFromBuffer(buffer, { width: size, height: size });
      }

      tray = new Tray(trayIcon);
      console.log('System tray created successfully');
    } catch (error) {
      console.log('Tray creation failed, continuing without tray:', error.message);
      tray = null;
    }

    // Only create menu if tray was created successfully
    if (!tray) {
      console.log('Skipping tray menu creation - no tray available');
      return;
    }

    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'YSViewer',
        type: 'normal',
        enabled: false
      },
      { type: 'separator' },
      {
        label: isMuted ? 'Unmute Application' : 'Mute Application',
        type: 'normal',
        click: () => this.toggleSystemAudio()
      },
      { type: 'separator' },
      {
        label: 'Show Window',
        type: 'normal',
        click: () => this.showMainWindow()
      },
      {
        label: 'Minimize to Dot',
        type: 'normal',
        click: () => this.minimizeToDot()
      },
      { type: 'separator' },
      {
        label: 'Quit',
        type: 'normal',
        click: () => {
          app.isQuiting = true;
          app.quit();
        }
      }
    ]);

    tray.setContextMenu(contextMenu);
    tray.setToolTip('YSViewer - YouTube & Spotify Player');

    // Handle tray click
    tray.on('click', () => {
      if (mainWindow.isVisible()) {
        this.minimizeToTray();
      } else {
        this.showMainWindow();
      }
    });
  }

  setupBrowserViews() {
    const bounds = mainWindow.getBounds();
    const viewWidth = Math.floor(bounds.width / 2);
    const viewHeight = bounds.height - 100; // Leave space for controls

    // YouTube view
    youtubeView = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    mainWindow.setBrowserView(youtubeView);
    youtubeView.setBounds({ x: 0, y: 100, width: viewWidth, height: viewHeight });
    youtubeView.webContents.loadURL('https://www.youtube.com');

    // Register YouTube view for audio control, ad skipping, and progress monitoring
    audioController.registerWebContents(youtubeView.webContents);
    youtubeAdSkipper.registerWebContents(youtubeView.webContents);
    videoProgressMonitor.registerWebContents(youtubeView.webContents);

    // Set up video switch callback
    videoProgressMonitor.setVideoSwitchCallback((progressInfo) => {
      console.log(`Video switch triggered at ${progressInfo.progress.toFixed(2)}%`);
      this.playNextVideo();
    });

    console.log('YouTube web contents registered for audio control, ad skipping, and progress monitoring');

    // Spotify view
    spotifyView = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    mainWindow.addBrowserView(spotifyView);
    spotifyView.setBounds({ x: viewWidth, y: 100, width: viewWidth, height: viewHeight });

    // Register Spotify view for audio control
    audioController.registerWebContents(spotifyView.webContents);
    console.log('Spotify web contents registered for audio control');

    // Clear Spotify session data if in development mode
    if (this.shouldClearSpotifySession) {
      spotifyView.webContents.once('dom-ready', async () => {
        try {
          await spotifyView.webContents.session.clearStorageData({
            storages: ['cookies', 'localstorage', 'sessionstorage', 'indexdb', 'websql']
          });
          console.log('[Dev Mode] Cleared Spotify browser session data');
        } catch (error) {
          console.error('[Dev Mode] Error clearing Spotify browser session:', error);
        }
      });
      this.shouldClearSpotifySession = false;
    }

    // Check if we have saved Spotify credentials
    this.initializeSpotifyLogin();

    // Handle window resize
    mainWindow.on('resize', () => {
      const newBounds = mainWindow.getBounds();
      const newViewWidth = Math.floor(newBounds.width / 2);
      const newViewHeight = newBounds.height - 100;

      youtubeView.setBounds({ x: 0, y: 100, width: newViewWidth, height: newViewHeight });
      spotifyView.setBounds({ x: newViewWidth, y: 100, width: newViewWidth, height: newViewHeight });
    });

    // Handle external links and auto-accept cookies
    [youtubeView, spotifyView].forEach((view, index) => {
      const platform = index === 0 ? 'YouTube' : 'Spotify';

      view.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
      });

      // Auto-accept cookies when page loads
      view.webContents.on('dom-ready', () => {
        // Re-apply mute state when DOM is ready
        if (isMuted) {
          view.webContents.setAudioMuted(true);
          console.log(`Re-applied mute state to ${platform} on DOM ready`);
        }

        // Restart ad detection for YouTube
        if (platform === 'YouTube' && youtubeAdSkipper) {
          youtubeAdSkipper.startAdDetection();
        }

        const appInstance = YSViewerApp.getInstance();
        appInstance.autoAcceptCookies(view.webContents, platform);
      });

      // Also try after navigation
      view.webContents.on('did-finish-load', () => {
        // Re-apply mute state after page loads
        if (isMuted) {
          view.webContents.setAudioMuted(true);
          console.log(`Re-applied mute state to ${platform} after page load`);
        }

        // Restart ad detection for YouTube after page loads
        if (platform === 'YouTube' && youtubeAdSkipper) {
          setTimeout(() => {
            youtubeAdSkipper.startAdDetection();
          }, 1000); // Wait 1 second for page to stabilize
        }

        setTimeout(() => {
          const appInstance = YSViewerApp.getInstance();
          appInstance.autoAcceptCookies(view.webContents, platform);
        }, 2000); // Wait 2 seconds for cookie banners to appear
      });
    });
  }

  async startPlaylistManagement() {
    try {
      await playlistManager.initialize();
      
      // Start playing immediately
      setTimeout(() => {
        this.playNextVideo();
      }, 3000); // Wait 3 seconds for views to load

      // Set up periodic playlist checking
      setInterval(() => {
        this.checkAndPlayNext();
      }, 3600000); // Check every hour
    } catch (error) {
      console.error('Failed to start playlist management:', error);
    }
  }

  async playNextVideo() {
    try {
      // Use improved random selection - try mandatory first, then random pool
      let nextYouTubeVideo = await playlistManager.getNextMandatoryVideo('YouTube');
      let nextSpotifyVideo = await playlistManager.getNextMandatoryVideo('Spotify');

      // If no mandatory videos available, try truly random selection
      if (!nextYouTubeVideo) {
        nextYouTubeVideo = await playlistManager.getAnyRandomVideo('YouTube');
      }
      if (!nextSpotifyVideo) {
        nextSpotifyVideo = await playlistManager.getAnyRandomVideo('Spotify');
      }

      // Play YouTube video if available
      if (nextYouTubeVideo && (nextYouTubeVideo.url.includes('youtube.com') || nextYouTubeVideo.url.includes('youtu.be'))) {
        console.log('Playing YouTube video:', nextYouTubeVideo.url);

        // Try to find and click recommended video link first
        const clickedRecommended = await this.tryClickRecommendedVideo(nextYouTubeVideo.url);

        if (!clickedRecommended) {
          // Fall back to direct URL loading if no recommended video found
          youtubeView.webContents.loadURL(nextYouTubeVideo.url);
        }

        // Notify progress monitor of new video
        if (videoProgressMonitor) {
          videoProgressMonitor.setCurrentVideo(nextYouTubeVideo.url, nextYouTubeVideo);
        }
      }

      // Play Spotify video if available - use separate login system
      if (nextSpotifyVideo && nextSpotifyVideo.url.includes('spotify.com')) {
        console.log('Playing Spotify track:', nextSpotifyVideo.url);
        await this.playSpotifyTrack(nextSpotifyVideo.url);
      }

      // If still no videos, try any available video from any pool
      if (!nextYouTubeVideo && !nextSpotifyVideo) {
        const anyVideo = await playlistManager.getAnyRandomVideo();
        if (anyVideo) {
          console.log('Playing any available video:', anyVideo.url);

          if (anyVideo.url.includes('youtube.com') || anyVideo.url.includes('youtu.be')) {
            // Try to find and click recommended video link first
            const clickedRecommended = await this.tryClickRecommendedVideo(anyVideo.url);

            if (!clickedRecommended) {
              // Fall back to direct URL loading if no recommended video found
              youtubeView.webContents.loadURL(anyVideo.url);
            }

            // Notify progress monitor of new video
            if (videoProgressMonitor) {
              videoProgressMonitor.setCurrentVideo(anyVideo.url, anyVideo);
            }
          } else if (anyVideo.url.includes('spotify.com')) {
            await this.playSpotifyTrack(anyVideo.url);
          }
        } else {
          console.log('No videos available to play');
        }
      }
    } catch (error) {
      console.error('Failed to play next video:', error);
    }
  }

  async checkAndPlayNext() {
    try {
      const shouldPlay = await playlistManager.shouldPlayNext();
      if (shouldPlay) {
        this.playNextVideo();
      }
    } catch (error) {
      console.error('Failed to check playlist:', error);
    }
  }

  /**
   * Try to find and click a recommended video link on the current YouTube page
   * @param {string} targetUrl - The URL we want to navigate to
   * @returns {boolean} - True if a recommended video was clicked, false otherwise
   */
  async tryClickRecommendedVideo(targetUrl) {
    try {
      if (!youtubeView || !youtubeView.webContents || youtubeView.webContents.isDestroyed()) {
        return false;
      }

      console.log('Searching for recommended video:', targetUrl);

      // Extract video ID from target URL
      const targetVideoId = this.extractVideoId(targetUrl);
      if (!targetVideoId) {
        console.log('Could not extract video ID from URL:', targetUrl);
        return false;
      }

      // Execute JavaScript to find and click recommended video
      const clickResult = await youtubeView.webContents.executeJavaScript(`
        (function() {
          const targetVideoId = '${targetVideoId}';
          console.log('Looking for video ID:', targetVideoId);

          // Find all video links on the page
          const videoLinks = document.querySelectorAll('a[href*="/watch?v="], a[href*="youtu.be/"]');
          console.log('Found', videoLinks.length, 'video links on page');

          for (let link of videoLinks) {
            const href = link.href;
            let videoId = '';

            // Extract video ID from different URL formats
            if (href.includes('/watch?v=')) {
              const match = href.match(/[?&]v=([^&]+)/);
              videoId = match ? match[1] : '';
            } else if (href.includes('youtu.be/')) {
              const match = href.match(/youtu\\.be\\/([^?&]+)/);
              videoId = match ? match[1] : '';
            }

            if (videoId === targetVideoId) {
              console.log('Found matching recommended video link:', href);

              // Preserve any tracking parameters from the current page
              const currentUrl = new URL(window.location.href);
              const linkUrl = new URL(href, window.location.origin);

              // Copy tracking parameters if they exist
              const trackingParams = ['list', 'index', 'pp', 'si', 'feature', 'app', 'itct'];
              trackingParams.forEach(param => {
                if (currentUrl.searchParams.has(param)) {
                  linkUrl.searchParams.set(param, currentUrl.searchParams.get(param));
                }
              });

              // Update the link href with tracking parameters
              link.href = linkUrl.toString();

              // Click the link to navigate naturally
              console.log('Clicking recommended video with tracking:', link.href);
              link.click();
              return true;
            }
          }

          console.log('No matching recommended video found for ID:', targetVideoId);
          return false;
        })();
      `);

      if (clickResult) {
        console.log('Successfully clicked recommended video link');
        return true;
      } else {
        console.log('No matching recommended video found on current page');
        return false;
      }
    } catch (error) {
      console.error('Error trying to click recommended video:', error);
      return false;
    }
  }

  /**
   * Extract video ID from YouTube URL
   * @param {string} url - YouTube URL
   * @returns {string|null} - Video ID or null if not found
   */
  extractVideoId(url) {
    try {
      const urlObj = new URL(url);

      // Handle youtube.com/watch?v= format
      if (urlObj.hostname.includes('youtube.com') && urlObj.pathname === '/watch') {
        return urlObj.searchParams.get('v');
      }

      // Handle youtu.be/ format
      if (urlObj.hostname === 'youtu.be') {
        return urlObj.pathname.substring(1); // Remove leading slash
      }

      return null;
    } catch (error) {
      console.error('Error extracting video ID from URL:', url, error);
      return null;
    }
  }

  /**
   * Play Spotify track with enhanced login handling
   * @param {string} url - Spotify track URL
   */
  async playSpotifyTrack(url) {
    try {
      if (!spotifyView || !spotifyView.webContents || spotifyView.webContents.isDestroyed()) {
        console.error('Spotify view not available');
        return;
      }

      // First check if user is already logged in
      const isLoggedIn = await this.isSpotifyLoggedIn();
      if (isLoggedIn) {
        console.log('User already logged in to Spotify, playing track directly');
        spotifyView.webContents.loadURL(url);
        return;
      }

      console.log('[Spotify Login] User not logged in, attempting automatic login');

      // Try automatic replay first
      const recording = await this.spotifyRecorder.loadRecording();
      if (recording && recording.interactions && recording.interactions.length > 0) {
        console.log('[Spotify Login] Found recorded login flow, attempting automatic replay');

        const replaySuccess = await this.attemptAutomaticReplay(url, recording);
        if (replaySuccess) {
          console.log('[Spotify Login] Automatic replay successful');
          return;
        }

        console.warn('[Spotify Login] Automatic replay failed, falling back to manual login');
        await this.showLoginFailureWarning('Automatic login failed. Please login manually to update the login flow.');
      } else {
        console.log('[Spotify Login] No recorded login flow found');
        await this.showLoginFailureWarning('No login flow recorded. Please login manually to record the login process.');
      }

      // Fall back to manual recording
      console.log('[Spotify Login] Starting manual login with recording');
      await this.recordSpotifyLogin(url);

    } catch (error) {
      console.error('Error playing Spotify track:', error);
      await this.showLoginFailureWarning('Login error occurred. Please try logging in manually.');
    }
  }

  /**
   * Enhanced Spotify login detection
   * @returns {Promise<boolean>} - True if user is logged in to Spotify
   */
  async isSpotifyLoggedIn() {
    try {
      if (!spotifyView || !spotifyView.webContents || spotifyView.webContents.isDestroyed()) {
        return false;
      }

      const loginStatus = await spotifyView.webContents.executeJavaScript(`
        (async function() {
          const currentUrl = window.location.href;

          // Check URL patterns that indicate logged-in state
          const loggedInUrls = [
            'open.spotify.com',
            'spotify.com/track',
            'spotify.com/playlist',
            'spotify.com/album',
            'spotify.com/artist',
            'spotify.com/user'
          ];

          const isLoggedInUrl = loggedInUrls.some(pattern => currentUrl.includes(pattern));

          // Check for authentication cookies
          const hasAuthCookies = document.cookie.includes('sp_') ||
                                document.cookie.includes('spotify') ||
                                document.cookie.includes('oauth');

          // Check for login form presence (indicates NOT logged in)
          const loginForm = document.querySelector('#login-username') ||
                           document.querySelector('input[name="username"]') ||
                           document.querySelector('input[type="email"]') ||
                           document.querySelector('.login-form') ||
                           document.querySelector('[data-testid="login-username"]');

          // Check for user profile elements (indicates logged in)
          const userProfile = document.querySelector('.user-widget') ||
                             document.querySelector('[data-testid="user-widget"]') ||
                             document.querySelector('.profile-menu') ||
                             document.querySelector('.user-menu');

          // Check for Spotify Web Player elements
          const webPlayer = document.querySelector('.now-playing') ||
                           document.querySelector('[data-testid="now-playing-widget"]') ||
                           document.querySelector('.player-controls') ||
                           document.querySelector('.playback-bar');

          // Check localStorage for authentication tokens
          let hasAuthTokens = false;
          try {
            const accessToken = localStorage.getItem('accessToken') ||
                               localStorage.getItem('spotify_access_token') ||
                               localStorage.getItem('sp_access_token');
            hasAuthTokens = !!accessToken;
          } catch (e) {
            // localStorage access might be restricted
          }

          // Check sessionStorage for authentication data
          let hasSessionAuth = false;
          try {
            const sessionData = sessionStorage.getItem('spotify_auth') ||
                               sessionStorage.getItem('user_session') ||
                               sessionStorage.getItem('sp_session');
            hasSessionAuth = !!sessionData;
          } catch (e) {
            // sessionStorage access might be restricted
          }

          return {
            isLoggedInUrl,
            hasAuthCookies,
            hasLoginForm: !!loginForm,
            hasUserProfile: !!userProfile,
            hasWebPlayer: !!webPlayer,
            hasAuthTokens,
            hasSessionAuth,
            currentUrl,
            // Overall determination
            isLoggedIn: (isLoggedInUrl && !loginForm) ||
                       hasUserProfile ||
                       hasWebPlayer ||
                       (hasAuthCookies && !loginForm) ||
                       hasAuthTokens ||
                       hasSessionAuth
          };
        })();
      `);

      console.log('[Spotify Login Detection]', loginStatus);
      return loginStatus.isLoggedIn;
    } catch (error) {
      console.error('Error checking Spotify login status:', error);
      return false;
    }
  }

  /**
   * Get saved Spotify credentials from configuration
   * @returns {Object|null} - Credentials object or null if not found
   */
  getSpotifyCredentials() {
    try {
      const configPath = path.join(app.getPath('userData'), 'spotify-config.json');

      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        const config = JSON.parse(configData);
        return config.credentials || null;
      }

      return null;
    } catch (error) {
      console.error('Error reading Spotify credentials:', error);
      return null;
    }
  }

  /**
   * Save Spotify credentials to configuration file (cleartext as requested)
   * @param {string} username - Spotify username
   * @param {string} password - Spotify password
   */
  saveSpotifyCredentials(username, password) {
    try {
      const configPath = path.join(app.getPath('userData'), 'spotify-config.json');
      const config = {
        credentials: {
          username: username,
          password: password,
          savedAt: new Date().toISOString()
        }
      };

      fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
      console.log('Spotify credentials saved to:', configPath);
    } catch (error) {
      console.error('Error saving Spotify credentials:', error);
    }
  }

  /**
   * Record Spotify login interactions
   * @param {string} targetUrl - URL to play after login
   */
  async recordSpotifyLogin(targetUrl) {
    try {
      console.log('[Spotify Login] Starting interactive login with recording');

      // Start recording
      this.spotifyRecorder.startRecording();

      // Load Spotify login page
      spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      // Wait for page to load and inject recording script
      await new Promise(resolve => setTimeout(resolve, 3000));
      await this.spotifyRecorder.injectRecordingScript(spotifyView.webContents);

      // Set up IPC for recording interactions
      this.setupSpotifyRecordingIPC();

      // Start polling for interactions
      const pollInterval = setInterval(async () => {
        if (!this.spotifyRecorder.isRecording) {
          clearInterval(pollInterval);
          return;
        }
        await this.spotifyRecorder.pollRecordedInteractions(spotifyView.webContents);
      }, 1000);

      // Monitor for successful login
      const checkLoginStatus = async () => {
        const isLoggedIn = await this.isSpotifyLoggedIn();
        if (isLoggedIn) {
          console.log('[Spotify Login] Login successful, stopping recording');
          clearInterval(pollInterval);
          await this.spotifyRecorder.stopRecording();

          // Navigate to target URL
          spotifyView.webContents.loadURL(targetUrl);
          return;
        }

        // Check again in 2 seconds
        setTimeout(checkLoginStatus, 2000);
      };

      // Start monitoring after initial delay
      setTimeout(checkLoginStatus, 5000);

    } catch (error) {
      console.error('[Spotify Login] Error during recording:', error);
      await this.spotifyRecorder.stopRecording();
      // Fall back to direct URL loading
      spotifyView.webContents.loadURL(targetUrl);
    }
  }

  /**
   * Replay recorded Spotify login
   * @param {string} targetUrl - URL to play after login
   * @param {Object} recording - Recorded interactions
   */
  async replaySpotifyLogin(targetUrl, recording) {
    try {
      console.log('[Spotify Login] Starting automatic login replay');

      // Load Spotify login page
      spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      // Wait for page to load
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Replay the recorded interactions
      const success = await this.spotifyReplayer.replayInteractions(
        recording.interactions,
        spotifyView.webContents
      );

      if (success) {
        console.log('[Spotify Login] Replay completed, checking login status');

        // Wait a bit for login to process
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check if login was successful
        const isLoggedIn = await this.isSpotifyLoggedIn();
        if (isLoggedIn) {
          console.log('[Spotify Login] Replay login successful');
          spotifyView.webContents.loadURL(targetUrl);
        } else {
          console.log('[Spotify Login] Replay login failed, falling back to recording');
          await this.recordSpotifyLogin(targetUrl);
        }
      } else {
        console.log('[Spotify Login] Replay failed, falling back to recording');
        await this.recordSpotifyLogin(targetUrl);
      }

    } catch (error) {
      console.error('[Spotify Login] Error during replay:', error);
      // Fall back to recording
      await this.recordSpotifyLogin(targetUrl);
    }
  }

  /**
   * Setup IPC for Spotify recording
   */
  setupSpotifyRecordingIPC() {
    // Remove existing listeners to prevent duplicates
    ipcMain.removeAllListeners('record-spotify-interaction');

    ipcMain.on('record-spotify-interaction', (event, interaction) => {
      if (this.spotifyRecorder.isRecording) {
        this.spotifyRecorder.recordInteraction(interaction);
      }
    });
  }

  /**
   * Show Spotify login page and wait for user input
   * @param {string} targetUrl - The Spotify URL to play after login
   */
  async showSpotifyLoginPage(targetUrl) {
    try {
      // Load Spotify login page
      spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      // Wait for login completion
      return new Promise((resolve) => {
        const checkLoginStatus = async () => {
          try {
            const currentUrl = spotifyView.webContents.getURL();

            // Check if we're logged in (redirected to Spotify main page or the target URL)
            if (currentUrl.includes('open.spotify.com') || currentUrl.includes('spotify.com/track') || currentUrl.includes('spotify.com/playlist')) {
              console.log('Spotify login detected, attempting to extract credentials');

              // Try to extract credentials from the login form if still visible
              const credentials = await this.extractSpotifyCredentials();
              if (credentials) {
                this.saveSpotifyCredentials(credentials.username, credentials.password);
              }

              // Navigate to target URL
              spotifyView.webContents.loadURL(targetUrl);
              resolve();
            } else {
              // Still on login page, check again in 2 seconds
              setTimeout(checkLoginStatus, 2000);
            }
          } catch (error) {
            console.error('Error checking Spotify login status:', error);
            setTimeout(checkLoginStatus, 2000);
          }
        };

        // Start checking after 3 seconds to allow page to load
        setTimeout(checkLoginStatus, 3000);
      });
    } catch (error) {
      console.error('Error showing Spotify login page:', error);
    }
  }

  async toggleSystemAudio() {
    try {
      isMuted = await audioController.toggleMute();
      this.updateTrayMenu();

      // Notify renderer process
      if (mainWindow && !mainWindow.isDestroyed()) {
        try {
          mainWindow.webContents.send('audio-state-changed', { isMuted });
        } catch (sendError) {
          console.warn('Failed to send audio state to renderer:', sendError.message);
        }
      }

      return isMuted;
    } catch (error) {
      console.error('Failed to toggle audio:', error);
      throw new Error(`Audio toggle failed: ${error.message}`);
    }
  }

  updateTrayMenu() {
    if (tray && !tray.isDestroyed()) {
      const contextMenu = Menu.buildFromTemplate([
        {
          label: 'YSViewer',
          type: 'normal',
          enabled: false
        },
        { type: 'separator' },
        {
          label: isMuted ? 'Unmute Application' : 'Mute Application',
          type: 'normal',
          click: () => this.toggleSystemAudio()
        },
        { type: 'separator' },
        {
          label: 'Show Window',
          type: 'normal',
          click: () => this.showMainWindow()
        },
        {
          label: 'Minimize to Dot',
          type: 'normal',
          click: () => this.minimizeToDot()
        },
        { type: 'separator' },
        {
          label: 'Quit',
          type: 'normal',
          click: () => {
            app.isQuiting = true;
            app.quit();
          }
        }
      ]);
      tray.setContextMenu(contextMenu);
    }
  }

  showMainWindow() {
    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      if (mainWindow) {
        if (isMinimized) {
          // Restore from minimized state
          mainWindow.setSize(1400, 800);
          mainWindow.center();
          isMinimized = false;
        }
        mainWindow.show();
        mainWindow.focus();
        result = { success: true, windowVisible: true, wasMinimized: isMinimized };
      } else {
        result = { success: false, error: 'No main window available' };
      }
      return result;
    } catch (err) {
      error = err;
      throw err;
    } finally {
      executionCollector.collect(
        'showMainWindow',
        {},
        result,
        error,
        {
          executionTime: Date.now() - startTime,
          hasMainWindow: !!mainWindow,
          wasMinimized: isMinimized
        }
      );
    }
  }

  minimizeToTray() {
    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      if (mainWindow) {
        mainWindow.hide();
        result = { success: true, windowHidden: true };
      } else {
        result = { success: false, error: 'No main window available' };
      }
      return result;
    } catch (err) {
      error = err;
      throw err;
    } finally {
      executionCollector.collect(
        'minimizeToTray',
        {},
        result,
        error,
        {
          executionTime: Date.now() - startTime,
          hasMainWindow: !!mainWindow
        }
      );
    }
  }

  minimizeToDot() {
    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      if (mainWindow) {
        const { width, height } = screen.getPrimaryDisplay().workAreaSize;
        mainWindow.setSize(50, 50); // Changed from 5x5 to 50x50
        mainWindow.setPosition(width - 50, height - 50);
        mainWindow.setAlwaysOnTop(true);
        mainWindow.setSkipTaskbar(true);
        isMinimized = true;
        result = {
          success: true,
          windowMinimized: true,
          position: { x: width - 50, y: height - 50 },
          size: { width: 50, height: 50 }
        };
      } else {
        result = { success: false, error: 'No main window available' };
      }
      return result;
    } catch (err) {
      error = err;
      throw err;
    } finally {
      executionCollector.collect(
        'minimizeToDot',
        {},
        result,
        error,
        {
          executionTime: Date.now() - startTime,
          hasMainWindow: !!mainWindow,
          screenSize: mainWindow ? screen.getPrimaryDisplay().workAreaSize : null
        }
      );
    }
  }

  async initializeSpotifyLogin() {
    try {
      // Check if we have saved credentials
      const savedCredentials = store.get('spotifyCredentials');

      if (savedCredentials && savedCredentials.username && savedCredentials.password) {
        console.log('Found saved Spotify credentials, attempting auto-login');
        await this.loginToSpotify(savedCredentials.username, savedCredentials.password);
      } else {
        console.log('No saved Spotify credentials, showing login form');
        await this.showSpotifyLoginForm();
      }
    } catch (error) {
      console.error('Failed to initialize Spotify login:', error);
      await this.showSpotifyLoginForm();
    }
  }

  async showSpotifyLoginForm() {
    // Load a custom login page first
    const loginHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Spotify Login - YSViewer</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            background: #1db954;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
          }
          .login-container {
            background: rgba(0,0,0,0.8);
            padding: 40px;
            border-radius: 10px;
            text-align: center;
            max-width: 400px;
            width: 100%;
          }
          input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
          }
          button {
            background: #1db954;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
          }
          button:hover { background: #1ed760; }
          .skip-btn { background: #666; }
          .skip-btn:hover { background: #888; }
          h2 { margin-bottom: 20px; }
          .note { font-size: 12px; color: #ccc; margin-top: 15px; }
        </style>
      </head>
      <body>
        <div class="login-container">
          <h2>🎵 Spotify Login</h2>
          <p>Enter your Spotify credentials to enable automatic playback</p>
          <form id="loginForm">
            <input type="text" id="username" placeholder="Username or Email" required>
            <input type="password" id="password" placeholder="Password" required>
            <div>
              <button type="submit">Login</button>
              <button type="button" class="skip-btn" onclick="skipLogin()">Skip</button>
            </div>
          </form>
          <div class="note">
            Your credentials are stored locally and encrypted.<br>
            You can change them later in settings.
          </div>
        </div>
        <script>
          document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username && password) {
              // Send credentials to main process
              window.electronAPI?.loginSpotify?.(username, password);
            }
          });

          function skipLogin() {
            window.electronAPI?.skipSpotifyLogin?.();
          }
        </script>
      </body>
      </html>
    `;

    // Create a data URL for the login page
    const dataURL = 'data:text/html;charset=utf-8,' + encodeURIComponent(loginHTML);
    spotifyView.webContents.loadURL(dataURL);

    // Set up IPC for login
    spotifyView.webContents.on('dom-ready', () => {
      spotifyView.webContents.executeJavaScript(`
        window.electronAPI = {
          loginSpotify: (username, password) => {
            console.log('Login attempt with username:', username);
            // This will be handled by the main process
          },
          skipSpotifyLogin: () => {
            console.log('Skipping Spotify login');
            // Load Spotify without login
          }
        };
      `);
    });
  }

  /**
   * Extract Spotify credentials from login form
   * @returns {Object|null} - Credentials object or null if not found
   */
  async extractSpotifyCredentials() {
    try {
      const credentials = await spotifyView.webContents.executeJavaScript(`
        (function() {
          const usernameField = document.querySelector('#login-username');
          const passwordField = document.querySelector('#login-password');

          if (usernameField && passwordField && usernameField.value && passwordField.value) {
            return {
              username: usernameField.value,
              password: passwordField.value
            };
          }

          return null;
        })();
      `);

      return credentials;
    } catch (error) {
      console.error('Error extracting Spotify credentials:', error);
      return null;
    }
  }

  /**
   * Login to Spotify with credentials and play target URL
   * @param {string} targetUrl - The Spotify URL to play after login
   * @param {string} username - Spotify username
   * @param {string} password - Spotify password
   */
  async loginAndPlaySpotify(targetUrl, username, password) {
    try {
      console.log('Attempting Spotify login for user:', username);

      // Load Spotify login page
      spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      // Wait for login page to load
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Fill in login form and submit
      const loginSuccess = await spotifyView.webContents.executeJavaScript(`
        (function() {
          try {
            const usernameField = document.querySelector('#login-username');
            const passwordField = document.querySelector('#login-password');
            const loginButton = document.querySelector('#login-button');

            if (!usernameField || !passwordField || !loginButton) {
              console.error('Could not find login form elements');
              return false;
            }

            // Fill in credentials
            usernameField.value = '${username}';
            passwordField.value = '${password}';

            // Trigger events to ensure form validation works
            usernameField.dispatchEvent(new Event('input', { bubbles: true }));
            passwordField.dispatchEvent(new Event('input', { bubbles: true }));

            // Submit form
            loginButton.click();
            return true;
          } catch (error) {
            console.error('Error during login form submission:', error);
            return false;
          }
        })();
      `);

      if (loginSuccess) {
        console.log('Login form submitted, waiting for redirect...');

        // Wait for login to complete and redirect
        await new Promise(resolve => {
          const checkLoginStatus = () => {
            const currentUrl = spotifyView.webContents.getURL();
            if (currentUrl.includes('open.spotify.com') || currentUrl.includes('spotify.com/track') || currentUrl.includes('spotify.com/playlist')) {
              console.log('Login successful, redirecting to target URL');
              resolve();
            } else {
              setTimeout(checkLoginStatus, 1000);
            }
          };

          setTimeout(checkLoginStatus, 3000);
        });

        // Navigate to target URL
        spotifyView.webContents.loadURL(targetUrl);
      } else {
        console.error('Failed to submit login form');
        spotifyView.webContents.loadURL(targetUrl);
      }
    } catch (error) {
      console.error('Error during Spotify login:', error);
      // Fall back to direct URL loading
      spotifyView.webContents.loadURL(targetUrl);
    }
  }

  async loginToSpotify(username, password) {
    try {
      console.log('Attempting Spotify login for user:', username);

      // Save credentials for future use
      this.saveSpotifyCredentials(username, password);

      // Load Spotify login page
      spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      // Wait for page to load and then fill in credentials
      spotifyView.webContents.once('did-finish-load', async () => {
        setTimeout(async () => {
          try {
            await spotifyView.webContents.executeJavaScript(`
              (function() {
                // Fill in username
                const usernameField = document.querySelector('#login-username') ||
                                    document.querySelector('input[name="username"]') ||
                                    document.querySelector('input[type="text"]') ||
                                    document.querySelector('input[type="email"]');

                if (usernameField) {
                  usernameField.value = '${username}';
                  usernameField.dispatchEvent(new Event('input', { bubbles: true }));
                }

                // Fill in password
                const passwordField = document.querySelector('#login-password') ||
                                    document.querySelector('input[name="password"]') ||
                                    document.querySelector('input[type="password"]');

                if (passwordField) {
                  passwordField.value = '${password}';
                  passwordField.dispatchEvent(new Event('input', { bubbles: true }));
                }

                // Submit form
                setTimeout(() => {
                  const loginButton = document.querySelector('#login-button') ||
                                    document.querySelector('button[type="submit"]') ||
                                    document.querySelector('button:contains("Log In")') ||
                                    document.querySelector('.btn-green');

                  if (loginButton) {
                    loginButton.click();
                  }
                }, 500);

                return true;
              })();
            `);

            // Save credentials if login appears successful
            store.set('spotifyCredentials', { username, password });
            console.log('Spotify credentials saved');

          } catch (error) {
            console.error('Failed to auto-fill Spotify login:', error);
          }
        }, 2000);
      });

    } catch (error) {
      console.error('Failed to login to Spotify:', error);
      throw error;
    }
  }

  async autoAcceptCookies(webContents, platform) {
    try {
      // Check if cookies have already been accepted for this platform
      const cookieAcceptanceKey = `cookiesAccepted_${platform}`;
      const alreadyAccepted = store.get(cookieAcceptanceKey, false);

      if (alreadyAccepted) {
        console.log(`Cookies already accepted for ${platform}, skipping auto-accept`);
        return;
      }

      console.log(`Attempting to auto-accept cookies for ${platform}`);

      let cookieAccepted = false;

      if (platform === 'YouTube') {
        // YouTube cookie acceptance
        cookieAccepted = await webContents.executeJavaScript(`
          (function() {
            // Try multiple selectors for YouTube cookie consent
            const selectors = [
              'button[aria-label*="Accept"]',
              'button[aria-label*="accept"]',
              'button:contains("Accept all")',
              'button:contains("I agree")',
              'button:contains("Accept")',
              '[data-testid="accept-button"]',
              '.VfPpkd-LgbsSe[jsname="tWT92d"]', // YouTube's accept button
              'button[jsname="tWT92d"]'
            ];

            for (let selector of selectors) {
              const button = document.querySelector(selector);
              if (button && button.offsetParent !== null) {
                console.log('Found YouTube cookie accept button:', selector);
                button.click();
                return true;
              }
            }

            // Try finding by text content
            const buttons = document.querySelectorAll('button');
            for (let button of buttons) {
              const text = button.textContent.toLowerCase();
              if (text.includes('accept') || text.includes('agree') || text.includes('allow')) {
                console.log('Found YouTube cookie button by text:', text);
                button.click();
                return true;
              }
            }

            return false;
          })();
        `);
      } else if (platform === 'Spotify') {
        // Spotify cookie acceptance
        cookieAccepted = await webContents.executeJavaScript(`
          (function() {
            // Try multiple selectors for Spotify cookie consent
            const selectors = [
              'button[data-testid="accept-all-cookies"]',
              'button[id*="accept"]',
              'button:contains("Accept all")',
              'button:contains("Accept cookies")',
              'button:contains("I agree")',
              'button:contains("Accept")',
              '.onetrust-accept-btn-handler',
              '#onetrust-accept-btn-handler',
              '[data-cy="accept-all-cookies"]'
            ];

            for (let selector of selectors) {
              const button = document.querySelector(selector);
              if (button && button.offsetParent !== null) {
                console.log('Found Spotify cookie accept button:', selector);
                button.click();
                return true;
              }
            }

            // Try finding by text content
            const buttons = document.querySelectorAll('button');
            for (let button of buttons) {
              const text = button.textContent.toLowerCase();
              if (text.includes('accept') || text.includes('agree') || text.includes('allow')) {
                console.log('Found Spotify cookie button by text:', text);
                button.click();
                return true;
              }
            }

            return false;
          })();
        `);
      }

      // If cookies were successfully accepted, mark it as completed
      if (cookieAccepted) {
        store.set(cookieAcceptanceKey, true);
        console.log(`Cookies accepted for ${platform} - marked as completed, will not try again`);
      }
    } catch (error) {
      console.log(`Failed to auto-accept cookies for ${platform}:`, error.message);
    }
  }

  // Utility method to reset cookie acceptance flags (for testing/troubleshooting)
  resetCookieAcceptance(platform = null) {
    if (platform) {
      const cookieAcceptanceKey = `cookiesAccepted_${platform}`;
      store.delete(cookieAcceptanceKey);
      console.log(`Reset cookie acceptance flag for ${platform}`);
    } else {
      // Reset all platforms
      store.delete('cookiesAccepted_YouTube');
      store.delete('cookiesAccepted_Spotify');
      console.log('Reset cookie acceptance flags for all platforms');
    }
  }

  /**
   * Clear all Spotify session data for development mode
   */
  async clearSpotifySessionData() {
    try {
      // Clear electron-store credentials
      store.delete('spotifyCredentials');
      console.log('[Dev Mode] Cleared electron-store Spotify credentials');

      // Clear file-based credentials
      const configPath = path.join(app.getPath('userData'), 'spotify-config.json');
      if (fs.existsSync(configPath)) {
        fs.unlinkSync(configPath);
        console.log('[Dev Mode] Cleared file-based Spotify credentials');
      }

      // Clear recorded login flows (optional - keep for testing replay)
      // const recordingPath = path.join(app.getPath('userData'), 'spotify-login-recording.json');
      // if (fs.existsSync(recordingPath)) {
      //   fs.unlinkSync(recordingPath);
      //   console.log('[Dev Mode] Cleared Spotify login recording');
      // }

      // Clear any Spotify session data from the browser view when it's created
      this.shouldClearSpotifySession = true;

      console.log('[Dev Mode] Spotify session data cleared successfully');
    } catch (error) {
      console.error('[Dev Mode] Error clearing Spotify session data:', error);
    }
  }

  /**
   * Login to Spotify with provided credentials (UI-based)
   * @param {string} username - Spotify username
   * @param {string} password - Spotify password
   */
  async loginToSpotifyWithCredentials(username, password) {
    try {
      if (!spotifyView || !spotifyView.webContents || spotifyView.webContents.isDestroyed()) {
        throw new Error('Spotify view not available');
      }

      console.log('[Spotify Login] Starting login with UI credentials for user:', username);

      // Load Spotify login page
      spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      // Wait for page to load and then fill in credentials
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Login timeout'));
        }, 30000);

        spotifyView.webContents.once('did-finish-load', async () => {
          try {
            // Wait a bit for the page to fully render
            await new Promise(resolve => setTimeout(resolve, 2000));

            const loginSuccess = await spotifyView.webContents.executeJavaScript(`
              (function() {
                try {
                  const usernameField = document.querySelector('#login-username') ||
                                      document.querySelector('input[name="username"]') ||
                                      document.querySelector('input[type="text"]') ||
                                      document.querySelector('input[type="email"]');

                  const passwordField = document.querySelector('#login-password') ||
                                      document.querySelector('input[name="password"]') ||
                                      document.querySelector('input[type="password"]');

                  if (!usernameField || !passwordField) {
                    console.error('Could not find login form elements');
                    return false;
                  }

                  // Fill in credentials
                  usernameField.value = '${username}';
                  usernameField.dispatchEvent(new Event('input', { bubbles: true }));

                  passwordField.value = '${password}';
                  passwordField.dispatchEvent(new Event('input', { bubbles: true }));

                  // Submit form
                  setTimeout(() => {
                    const loginButton = document.querySelector('#login-button') ||
                                      document.querySelector('button[type="submit"]') ||
                                      document.querySelector('button:contains("Log In")') ||
                                      document.querySelector('.btn-green');

                    if (loginButton) {
                      loginButton.click();
                      return true;
                    }
                    return false;
                  }, 500);

                  return true;
                } catch (error) {
                  console.error('Login form error:', error);
                  return false;
                }
              })();
            `);

            if (loginSuccess) {
              // Wait for login to complete
              await this.waitForSpotifyLogin();
              clearTimeout(timeout);
              resolve();
            } else {
              clearTimeout(timeout);
              reject(new Error('Failed to fill login form'));
            }
          } catch (error) {
            clearTimeout(timeout);
            reject(error);
          }
        });
      });

      console.log('[Spotify Login] Login completed successfully');
    } catch (error) {
      console.error('[Spotify Login] Error during login:', error);
      throw error;
    }
  }

  /**
   * Start recording Spotify login flow
   */
  async startSpotifyLoginRecording() {
    try {
      if (!spotifyView || !spotifyView.webContents || spotifyView.webContents.isDestroyed()) {
        throw new Error('Spotify view not available');
      }

      console.log('[Spotify Recording] Starting login recording');
      await this.recordSpotifyLogin('https://open.spotify.com');
    } catch (error) {
      console.error('[Spotify Recording] Error starting recording:', error);
      throw error;
    }
  }

  /**
   * Logout from Spotify
   */
  async logoutFromSpotify() {
    try {
      if (!spotifyView || !spotifyView.webContents || spotifyView.webContents.isDestroyed()) {
        throw new Error('Spotify view not available');
      }

      console.log('[Spotify Logout] Starting logout process');

      // Navigate to Spotify logout URL
      spotifyView.webContents.loadURL('https://accounts.spotify.com/logout');

      // Wait a bit for logout to process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Clear session data
      await spotifyView.webContents.session.clearStorageData({
        storages: ['cookies', 'localstorage', 'sessionstorage', 'indexdb', 'websql']
      });

      console.log('[Spotify Logout] Logout completed successfully');
    } catch (error) {
      console.error('[Spotify Logout] Error during logout:', error);
      throw error;
    }
  }

  /**
   * Wait for Spotify login to complete
   */
  async waitForSpotifyLogin() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Login verification timeout'));
      }, 15000);

      const checkLoginStatus = async () => {
        try {
          const currentUrl = spotifyView.webContents.getURL();

          if (currentUrl.includes('open.spotify.com') ||
              currentUrl.includes('spotify.com/track') ||
              currentUrl.includes('spotify.com/playlist') ||
              currentUrl.includes('spotify.com/browse')) {
            clearTimeout(timeout);
            resolve();
          } else {
            setTimeout(checkLoginStatus, 1000);
          }
        } catch (error) {
          clearTimeout(timeout);
          reject(error);
        }
      };

      setTimeout(checkLoginStatus, 3000);
    });
  }

  /**
   * Attempt automatic replay of recorded login flow
   * @param {string} targetUrl - URL to navigate to after login
   * @param {Object} recording - Recorded login interactions
   * @returns {Promise<boolean>} - True if replay was successful
   */
  async attemptAutomaticReplay(targetUrl, recording) {
    try {
      console.log('[Spotify Replay] Starting automatic login replay');

      // Load Spotify login page
      spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      // Wait for page to load
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Replay the recorded interactions
      const success = await this.spotifyReplayer.replayInteractions(
        recording.interactions,
        spotifyView.webContents
      );

      if (!success) {
        console.warn('[Spotify Replay] Interaction replay failed');
        return false;
      }

      console.log('[Spotify Replay] Interactions replayed, checking login status');

      // Wait a bit for login to process
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Check if login was successful
      const isLoggedIn = await this.isSpotifyLoggedIn();
      if (isLoggedIn) {
        console.log('[Spotify Replay] Automatic login successful');
        spotifyView.webContents.loadURL(targetUrl);
        return true;
      } else {
        console.warn('[Spotify Replay] Login verification failed');
        return false;
      }

    } catch (error) {
      console.error('[Spotify Replay] Error during automatic replay:', error);
      return false;
    }
  }

  /**
   * Show login failure warning to user
   * @param {string} message - Warning message to display
   */
  async showLoginFailureWarning(message) {
    try {
      // Send notification to renderer process
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('show-notification', {
          message: message,
          type: 'error',
          duration: 5000
        });
      }

      console.warn('[Spotify Login Warning]', message);
    } catch (error) {
      console.error('[Spotify Login Warning] Error showing warning:', error);
    }
  }
}

// App event handlers
app.whenReady().then(() => {
  YSViewerApp.getInstance();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    YSViewerApp.getInstance().showMainWindow();
  }
});

// IPC handlers
ipcMain.handle('get-app-state', () => {
  return {
    isMuted,
    isMinimized
  };
});

ipcMain.handle('toggle-audio', async () => {
  const startTime = Date.now();
  let result = null;
  let error = null;

  try {
    const app = YSViewerApp.getInstance();
    await app.toggleSystemAudio();
    result = { success: true, isMuted };
    return result;
  } catch (err) {
    error = err;
    console.error('IPC toggle-audio error:', err);
    result = { success: false, error: err.message };
    return result;
  } finally {
    executionCollector.collect(
      'ipc_toggle_audio',
      {},
      result,
      error,
      {
        executionTime: Date.now() - startTime,
        previousMuteState: !isMuted
      }
    );
  }
});

ipcMain.handle('minimize-to-tray', () => {
  try {
    const app = YSViewerApp.getInstance();
    app.minimizeToTray();
    return { success: true };
  } catch (error) {
    console.error('IPC minimize-to-tray error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('close-application', () => {
  try {
    app.isQuiting = true;
    app.quit();
    return { success: true };
  } catch (error) {
    console.error('IPC close-application error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('minimize-to-dot', () => {
  try {
    const app = YSViewerApp.getInstance();
    app.minimizeToDot();
    return { success: true };
  } catch (error) {
    console.error('IPC minimize-to-dot error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('show-window', () => {
  try {
    const app = YSViewerApp.getInstance();
    app.showMainWindow();
    return { success: true };
  } catch (error) {
    console.error('IPC show-window error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('reset-cookie-acceptance', (_, platform) => {
  try {
    const app = YSViewerApp.getInstance();
    app.resetCookieAcceptance(platform);
    return { success: true };
  } catch (error) {
    console.error('IPC reset-cookie-acceptance error:', error);
    return { success: false, error: error.message };
  }
});

// Spotify login IPC handlers
ipcMain.handle('spotify-login', async (_, { username, password }) => {
  try {
    const app = YSViewerApp.getInstance();
    await app.loginToSpotifyWithCredentials(username, password);
    return { success: true };
  } catch (error) {
    console.error('IPC spotify-login error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('spotify-record-login', async () => {
  try {
    const app = YSViewerApp.getInstance();
    await app.startSpotifyLoginRecording();
    return { success: true };
  } catch (error) {
    console.error('IPC spotify-record-login error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('spotify-logout', async () => {
  try {
    const app = YSViewerApp.getInstance();
    await app.logoutFromSpotify();
    return { success: true };
  } catch (error) {
    console.error('IPC spotify-logout error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('verify-mute-state', () => {
  try {
    audioController.verifyMuteState();
    return { success: true };
  } catch (error) {
    console.error('IPC verify-mute-state error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('toggle-ad-skipper', () => {
  try {
    if (youtubeAdSkipper.isEnabled) {
      youtubeAdSkipper.disable();
    } else {
      youtubeAdSkipper.enable();
    }
    return { success: true, isEnabled: youtubeAdSkipper.isEnabled };
  } catch (error) {
    console.error('IPC toggle-ad-skipper error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-ad-skipper-stats', () => {
  try {
    const stats = youtubeAdSkipper.getStats();
    return { success: true, stats };
  } catch (error) {
    console.error('IPC get-ad-skipper-stats error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('reset-ad-skipper-stats', () => {
  try {
    youtubeAdSkipper.resetStats();
    return { success: true };
  } catch (error) {
    console.error('IPC reset-ad-skipper-stats error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('debug-ad-detection', async () => {
  try {
    await youtubeAdSkipper.debugAdDetection();
    return { success: true };
  } catch (error) {
    console.error('IPC debug-ad-detection error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-progress-stats', () => {
  try {
    const stats = videoProgressMonitor.getProgressStats();
    return { success: true, stats };
  } catch (error) {
    console.error('IPC get-progress-stats error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-progress-history', () => {
  try {
    const history = videoProgressMonitor.getProgressHistory();
    return { success: true, history };
  } catch (error) {
    console.error('IPC get-progress-history error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('toggle-progress-monitor', () => {
  try {
    if (videoProgressMonitor.isEnabled) {
      videoProgressMonitor.disable();
    } else {
      videoProgressMonitor.enable();
    }
    return { success: true, isEnabled: videoProgressMonitor.isEnabled };
  } catch (error) {
    console.error('IPC toggle-progress-monitor error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('reset-progress-monitor', () => {
  try {
    videoProgressMonitor.reset();
    return { success: true };
  } catch (error) {
    console.error('IPC reset-progress-monitor error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-ad-detection-history', () => {
  try {
    const history = youtubeAdSkipper.getAdDetectionHistory();
    return { success: true, history };
  } catch (error) {
    console.error('IPC get-ad-detection-history error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-skip-attempt-history', () => {
  try {
    const history = youtubeAdSkipper.getSkipAttemptHistory();
    return { success: true, history };
  } catch (error) {
    console.error('IPC get-skip-attempt-history error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-ad-skipper-mock-data', () => {
  try {
    const mockData = youtubeAdSkipper.getMockData();
    return { success: true, mockData };
  } catch (error) {
    console.error('IPC get-ad-skipper-mock-data error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('clear-ad-skipper-mock-data', () => {
  try {
    youtubeAdSkipper.clearMockData();
    return { success: true };
  } catch (error) {
    console.error('IPC clear-ad-skipper-mock-data error:', error);
    return { success: false, error: error.message };
  }
});
