/**
 * YouTube Ad Skipper
 * Detects and automatically skips YouTube ads when the skip button becomes available
 */

const executionCollector = require('./execution-collector');

class YouTubeAdSkipper {
  constructor() {
    this.isEnabled = true;
    this.checkInterval = null;
    this.adDetectionInterval = 500; // Check every 500ms for faster response
    this.webContents = null;
    this.adSkippedCount = 0;
    this.lastAdSkipTime = 0;
    this.isCurrentlySkipping = false;
    this.lastAdDetectionTime = 0;
    this.consecutiveAdDetections = 0;
    this.adDetectionHistory = []; // Store ad detection history for mock data
    this.skipAttemptHistory = []; // Store skip attempt history for mock data
  }

  /**
   * Register web contents for ad skipping
   * @param {Electron.WebContents} webContents - The YouTube web contents
   */
  registerWebContents(webContents) {
    this.webContents = webContents;
    console.log('YouTube Ad Skipper: Web contents registered');
    this.startAdDetection();
  }

  /**
   * Unregister web contents
   */
  unregisterWebContents() {
    this.stopAdDetection();
    this.webContents = null;
    console.log('YouTube Ad Skipper: Web contents unregistered');
  }

  /**
   * Start the ad detection process
   */
  startAdDetection() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    if (!this.isEnabled || !this.webContents) {
      return;
    }

    console.log('YouTube Ad Skipper: Starting ad detection');
    
    this.checkInterval = setInterval(() => {
      this.checkForAds();
    }, this.adDetectionInterval);
  }

  /**
   * Stop the ad detection process
   */
  stopAdDetection() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log('YouTube Ad Skipper: Stopped ad detection');
    }
  }

  /**
   * Check for ads and skip them if possible
   */
  async checkForAds() {
    if (!this.webContents || this.webContents.isDestroyed() || this.isCurrentlySkipping) {
      return;
    }

    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      // Add debug logging to track execution
      //console.log('YouTube Ad Skipper: Executing ad detection check...');

      const adInfo = await this.webContents.executeJavaScript(`
        (function() {
          console.log('YouTube Ad Skipper: JavaScript execution started');

          // Enhanced ad detection with more comprehensive checks
          const adDetection = {
            // Modern YouTube skip button selectors (2024)
            skipButtons: [
              '.ytp-skip-ad-button',
              '.ytp-ad-skip-button',
              '.ytp-ad-skip-button-modern',
              '.ytp-ad-skip-button-container .ytp-button',
              'button[class*="skip"]',
              'button[aria-label*="Skip"]',
              'button[aria-label*="skip"]',
              'button[data-tooltip-target-id*="skip"]',
              '.ytp-ad-skip-button-slot button',
              '[data-testid="skip-button"]'
            ],

            // Ad presence indicators
            adIndicators: [
              '.ytp-ad-overlay-container',
              '.ytp-ad-text',
              '.ytp-ad-duration-remaining',
              '.ytp-ad-preview-container',
              '.ytp-ad-player-overlay',
              '.ytp-ad-module',
              '.video-ads',
              '.ad-showing',
              '[class*="ad-overlay"]',
              '.ytp-ad-image-overlay'
            ]
          };

          // Check if an ad is currently playing
          let isAdPlaying = false;
          let skipButtonAvailable = false;
          let skipButton = null;
          let adTimeRemaining = null;
          let skipButtonText = '';
          let adType = 'unknown';

          console.log('YouTube Ad Skipper: Starting ad detection checks');

          // Method 1: Check for ad overlay elements
          for (const selector of adDetection.adIndicators) {
            const element = document.querySelector(selector);
            if (element && element.offsetParent !== null &&
                getComputedStyle(element).display !== 'none' &&
                getComputedStyle(element).visibility !== 'hidden') {
              isAdPlaying = true;
              adType = 'overlay';
              console.log('YouTube Ad Skipper: Ad detected via overlay selector:', selector);
              break;
            }
          }

          // Method 2: Check for "Ad" text in video player area
          if (!isAdPlaying) {
            const videoContainer = document.querySelector('.html5-video-container') ||
                                 document.querySelector('#movie_player') ||
                                 document.querySelector('.ytp-chrome-bottom');

            if (videoContainer) {
              const adTextElements = videoContainer.querySelectorAll('*');
              for (const element of adTextElements) {
                const text = element.textContent || '';
                const trimmedText = text.trim();

                // More specific ad text patterns
                if (trimmedText.match(/^Ad \\d+/) ||
                    trimmedText.includes('Ad ·') ||
                    trimmedText.includes('Advertisement') ||
                    trimmedText.includes('Skip Ad') ||
                    trimmedText.match(/^\\d+:\\d+.*Ad/)) {

                  if (element.offsetParent !== null &&
                      getComputedStyle(element).display !== 'none') {
                    isAdPlaying = true;
                    adType = 'text';
                    break;
                  }
                }
              }
            }
          }

          // Method 3: Check video element properties
          if (!isAdPlaying) {
            const video = document.querySelector('video');
            if (video && video.src) {
              // Check if video URL contains ad indicators
              if (video.src.includes('googleads') ||
                  video.src.includes('doubleclick') ||
                  video.src.includes('/ads/')) {
                isAdPlaying = true;
                adType = 'video-src';
              }
            }
          }

          // Method 4: Check for ad countdown timer
          if (!isAdPlaying) {
            const countdownElements = document.querySelectorAll('*');
            for (const element of countdownElements) {
              const text = element.textContent || '';
              if (text.match(/^\\d+$/) && element.parentElement) {
                const parentText = element.parentElement.textContent || '';
                if (parentText.includes('Ad') || parentText.includes('Skip')) {
                  isAdPlaying = true;
                  adType = 'countdown';
                  break;
                }
              }
            }
          }

          // Look for skip button if ad is detected
          if (isAdPlaying) {
            console.log('YouTube Ad Skipper: Ad detected, looking for skip button');
            for (const selector of adDetection.skipButtons) {
              const button = document.querySelector(selector);
              if (button) {
                const style = getComputedStyle(button);
                const isVisible = button.offsetParent !== null &&
                                style.display !== 'none' &&
                                style.visibility !== 'hidden' &&
                                style.opacity !== '0';

                const isClickable = !button.disabled &&
                                  !button.hasAttribute('disabled') &&
                                  !button.classList.contains('disabled');

                // Check if button has actual content
                const hasContent = button.textContent.trim() ||
                                 button.getAttribute('aria-label') ||
                                 button.querySelector('*');

                console.log('YouTube Ad Skipper: Found potential skip button:', {
                  selector: selector,
                  visible: isVisible,
                  clickable: isClickable,
                  hasContent: hasContent,
                  text: button.textContent.trim(),
                  ariaLabel: button.getAttribute('aria-label')
                });

                if (isVisible && isClickable && hasContent) {
                  skipButton = button;
                  skipButtonAvailable = true;
                  skipButtonText = button.textContent.trim() ||
                                 button.getAttribute('aria-label') ||
                                 'Skip';
                  break;
                }
              }
            }

            // If no skip button found by selector, try text-based search
            if (!skipButtonAvailable && isAdPlaying) {
              console.log('YouTube Ad Skipper: Trying text-based skip button detection');
              const allButtons = document.querySelectorAll('button');
              for (const button of allButtons) {
                const text = (button.textContent || '').toLowerCase().trim();
                const ariaLabel = (button.getAttribute('aria-label') || '').toLowerCase().trim();

                if ((text.includes('skip') || ariaLabel.includes('skip')) &&
                    button.offsetParent !== null &&
                    !button.disabled) {
                  skipButton = button;
                  skipButtonAvailable = true;
                  skipButtonText = button.textContent.trim() || ariaLabel;
                  console.log('YouTube Ad Skipper: Found skip button by text:', skipButtonText);
                  break;
                }
              }
            }

            // Get ad time remaining
            const timeSelectors = [
              '.ytp-ad-duration-remaining',
              '.ytp-ad-text',
              '[class*="countdown"]',
              '[class*="timer"]'
            ];

            for (const selector of timeSelectors) {
              const timeElement = document.querySelector(selector);
              if (timeElement && timeElement.textContent) {
                const text = timeElement.textContent.trim();
                if (text.match(/\d+/) || text.includes(':')) {
                  adTimeRemaining = text;
                  break;
                }
              }
            }
          }

          console.log('YouTube Ad Skipper: Final detection result:', {
            isAdPlaying: isAdPlaying,
            skipButtonAvailable: skipButtonAvailable,
            adType: adType
          });

          return {
            isAdPlaying,
            skipButtonAvailable,
            skipButtonSelector: skipButton ? (skipButton.className || skipButton.tagName) : null,
            skipButtonText,
            adTimeRemaining,
            adType,
            timestamp: Date.now(),
            debug: {
              totalButtons: document.querySelectorAll('button').length,
              skipButtons: adDetection.skipButtons.map(sel => {
                const btn = document.querySelector(sel);
                return btn ? {
                  selector: sel,
                  visible: btn.offsetParent !== null,
                  disabled: btn.disabled,
                  text: btn.textContent.trim()
                } : null;
              }).filter(Boolean)
            }
          };
        })();
      `);

      result = adInfo;

      // Store ad detection data for mock generation
      const detectionData = {
        timestamp: Date.now(),
        isAdPlaying: adInfo.isAdPlaying,
        skipButtonAvailable: adInfo.skipButtonAvailable,
        adType: adInfo.adType,
        skipButtonText: adInfo.skipButtonText,
        adTimeRemaining: adInfo.adTimeRemaining,
        consecutiveDetections: this.consecutiveAdDetections,
        debug: adInfo.debug
      };

      this.adDetectionHistory.push(detectionData);
      // Keep only last 50 detections for memory management
      if (this.adDetectionHistory.length > 50) {
        this.adDetectionHistory = this.adDetectionHistory.slice(-50);
      }

      if (adInfo.isAdPlaying) {
        const currentTime = Date.now();

        // Validate ad detection to reduce false positives
        if (currentTime - this.lastAdDetectionTime < 2000) {
          this.consecutiveAdDetections++;
        } else {
          this.consecutiveAdDetections = 1;
        }
        this.lastAdDetectionTime = currentTime;

        // Only log and act if we have consistent ad detection
        if (this.consecutiveAdDetections >= 2 || adInfo.skipButtonAvailable) {
          console.log('YouTube Ad Skipper: Ad detected', {
            type: adInfo.adType,
            skipAvailable: adInfo.skipButtonAvailable,
            skipButtonText: adInfo.skipButtonText,
            timeRemaining: adInfo.adTimeRemaining,
            consecutiveDetections: this.consecutiveAdDetections
          });

          if (adInfo.skipButtonAvailable) {
            await this.skipAd(adInfo);
          } else if (this.consecutiveAdDetections >= 3) {
            // Log debug info only after multiple consistent detections
            console.log('YouTube Ad Skipper: Skip button not available yet', {
              totalButtons: adInfo.debug.totalButtons,
              skipButtons: adInfo.debug.skipButtons.slice(0, 3) // Limit debug output
            });
          }
        }
      } else {
        // Reset consecutive detections when no ad is detected
        this.consecutiveAdDetections = 0;
        //console.log('YouTube Ad Skipper: No ad detected in this check');
      }

    } catch (err) {
      error = err;
      console.error('YouTube Ad Skipper: Error during ad detection:', err);
      console.error('YouTube Ad Skipper: Error stack:', err.stack);
      if (err.message.includes('destroyed')) {
        this.stopAdDetection();
      }
    } finally {
      // Collect execution data for mock generation
      executionCollector.collect(
        'checkForAds',
        {},
        result,
        error,
        {
          executionTime: Date.now() - startTime,
          consecutiveAdDetections: this.consecutiveAdDetections,
          isCurrentlySkipping: this.isCurrentlySkipping,
          adDetectionHistoryLength: this.adDetectionHistory.length
        }
      );
    }
  }

  /**
   * Skip the current ad using the most effective method
   */
  async skipAd(adInfo = null) {
    if (this.isCurrentlySkipping) {
      return;
    }

    this.isCurrentlySkipping = true;
    const startTime = Date.now();
    const currentTime = Date.now();
    let result = null;
    let error = null;

    console.log('YouTube Ad Skipper: Starting ad skip attempt');

    try {
      const skipped = await this.webContents.executeJavaScript(`
        (function() {
          console.log('YouTube Ad Skipper: Executing skip script');

          // Strategy 1: Try video manipulation first (most reliable)
          function skipVideoDirectly() {
            try {
              const video = document.querySelector('video');
              if (video && video.duration > 0) {
                console.log('YouTube Ad Skipper: Found video, duration:', video.duration, 'current time:', video.currentTime);

                // Skip to the very end
                video.currentTime = video.duration;

                // Trigger time update events
                video.dispatchEvent(new Event('timeupdate'));
                video.dispatchEvent(new Event('ended'));

                console.log('YouTube Ad Skipper: Video skipped to end');
                return true;
              }
            } catch (e) {
              console.warn('YouTube Ad Skipper: Video skip failed:', e.message);
            }
            return false;
          }

          // Strategy 2: Click skip button (backup method)
          function clickSkipButton() {
            console.log('YouTube Ad Skipper: Looking for skip buttons');

            // Most reliable skip button selectors
            const selectors = [
              '.ytp-skip-ad-button',
              '.ytp-ad-skip-button',
              'button[aria-label*="Skip"]',
              'button[aria-label*="skip"]'
            ];

            for (const selector of selectors) {
              const buttons = document.querySelectorAll(selector);
              console.log('YouTube Ad Skipper: Found', buttons.length, 'buttons for', selector);

              for (const button of buttons) {
                if (button.offsetParent !== null && !button.disabled) {
                  console.log('YouTube Ad Skipper: Clicking button:', button.textContent.trim(), button.getAttribute('aria-label'));

                  // Simple, direct click
                  button.click();

                  // Also dispatch mouse event as backup
                  button.dispatchEvent(new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true
                  }));

                  return true;
                }
              }
            }
            return false;
          }

          // Try video skip first, then button click
          let success = false;
          let method = '';

          if (skipVideoDirectly()) {
            success = true;
            method = 'video-manipulation';
            console.log('YouTube Ad Skipper: Successfully skipped via video manipulation');
          } else if (clickSkipButton()) {
            success = true;
            method = 'button-click';
            console.log('YouTube Ad Skipper: Successfully clicked skip button');
          } else {
            console.log('YouTube Ad Skipper: All skip methods failed');
          }

          return {
            success: success,
            method: method,
            timestamp: Date.now()
          };
        })();
      `);

      result = skipped;
      console.log('YouTube Ad Skipper: Skip result:', skipped);

      // Store skip attempt data for mock generation
      const skipAttemptData = {
        timestamp: Date.now(),
        adInfo: adInfo,
        skipResult: skipped,
        success: skipped && skipped.success,
        method: skipped ? skipped.method : null,
        executionTime: Date.now() - startTime
      };

      this.skipAttemptHistory.push(skipAttemptData);
      // Keep only last 30 skip attempts for memory management
      if (this.skipAttemptHistory.length > 30) {
        this.skipAttemptHistory = this.skipAttemptHistory.slice(-30);
      }

      if (skipped && skipped.success) {
        this.adSkippedCount++;
        this.lastAdSkipTime = currentTime;
        console.log(`YouTube Ad Skipper: ✅ Successfully skipped ad #${this.adSkippedCount} using ${skipped.method}`);

        // Brief pause after skipping to avoid rapid clicking
        setTimeout(() => {
          this.isCurrentlySkipping = false;
        }, 2000);
      } else {
        console.log('YouTube Ad Skipper: ❌ Failed to skip ad');
        this.isCurrentlySkipping = false;
      }

    } catch (err) {
      error = err;
      console.warn('YouTube Ad Skipper: Failed to skip ad:', err.message);
      this.isCurrentlySkipping = false;
    } finally {
      // Collect execution data for mock generation
      executionCollector.collect(
        'skipAd',
        { adInfo },
        result,
        error,
        {
          executionTime: Date.now() - startTime,
          adSkippedCount: this.adSkippedCount,
          skipAttemptHistoryLength: this.skipAttemptHistory.length,
          wasCurrentlySkipping: this.isCurrentlySkipping
        }
      );
    }
  }

  /**
   * Enable ad skipping
   */
  enable() {
    this.isEnabled = true;
    console.log('YouTube Ad Skipper: Enabled');
    if (this.webContents) {
      this.startAdDetection();
    }
  }

  /**
   * Disable ad skipping
   */
  disable() {
    this.isEnabled = false;
    this.stopAdDetection();
    console.log('YouTube Ad Skipper: Disabled');
  }

  /**
   * Get statistics about ad skipping
   */
  getStats() {
    return {
      isEnabled: this.isEnabled,
      adSkippedCount: this.adSkippedCount,
      lastAdSkipTime: this.lastAdSkipTime,
      isCurrentlySkipping: this.isCurrentlySkipping
    };
  }

  /**
   * Reset statistics
   */
  resetStats() {
    this.adSkippedCount = 0;
    this.lastAdSkipTime = 0;
    console.log('YouTube Ad Skipper: Statistics reset');
  }

  /**
   * Manually trigger ad detection for debugging
   */
  async debugAdDetection() {
    console.log('YouTube Ad Skipper: Manual debug detection triggered');
    await this.checkForAds();
  }

  /**
   * Get ad detection history for mock data generation
   */
  getAdDetectionHistory() {
    return [...this.adDetectionHistory];
  }

  /**
   * Get skip attempt history for mock data generation
   */
  getSkipAttemptHistory() {
    return [...this.skipAttemptHistory];
  }

  /**
   * Get comprehensive mock data for testing
   */
  getMockData() {
    return {
      adDetectionHistory: this.getAdDetectionHistory(),
      skipAttemptHistory: this.getSkipAttemptHistory(),
      stats: this.getStats(),
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * Clear collected mock data
   */
  clearMockData() {
    this.adDetectionHistory = [];
    this.skipAttemptHistory = [];
    console.log('YouTube Ad Skipper: Mock data cleared');
  }
}

module.exports = YouTubeAdSkipper;
