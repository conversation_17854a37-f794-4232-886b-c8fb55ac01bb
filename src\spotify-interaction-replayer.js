/**
 * SpotifyInteractionReplayer - Replays recorded user interactions for automated Spotify login
 */
class SpotifyInteractionReplayer {
  constructor() {
    this.isReplaying = false;
    this.currentInteractionIndex = 0;
    this.replayStartTime = null;
    this.maxRetries = 3;
    this.retryDelay = 1000;
  }

  /**
   * Replay recorded interactions
   * @param {Array} interactions - Array of recorded interactions
   * @param {Object} webContents - Electron webContents object
   * @returns {Promise<boolean>} Success status
   */
  async replayInteractions(interactions, webContents) {
    if (!interactions || interactions.length === 0) {
      console.log('[Spotify Replayer] No interactions to replay');
      return false;
    }

    this.isReplaying = true;
    this.currentInteractionIndex = 0;
    this.replayStartTime = Date.now();

    console.log(`[Spotify Replayer] Starting replay of ${interactions.length} interactions`);

    try {
      // Inject replay utilities into the page
      await this.injectReplayScript(webContents);

      // Process each interaction
      for (let i = 0; i < interactions.length; i++) {
        if (!this.isReplaying) {
          console.log('[Spotify Replayer] Replay cancelled');
          return false;
        }

        this.currentInteractionIndex = i;
        const interaction = interactions[i];
        
        console.log(`[Spotify Replayer] Replaying interaction ${i + 1}/${interactions.length}:`, interaction.type);

        // Wait for the appropriate timing
        if (i > 0) {
          const timeDiff = interaction.timestamp - interactions[i - 1].timestamp;
          const adjustedDelay = Math.min(Math.max(timeDiff, 100), 5000); // Between 100ms and 5s
          await this.delay(adjustedDelay);
        }

        // Execute the interaction
        const success = await this.executeInteraction(interaction, webContents);
        
        if (!success) {
          console.warn(`[Spotify Replayer] Failed to execute interaction ${i + 1}, continuing...`);
        }

        // Special handling for navigation - wait for page to load
        if (interaction.type === 'navigation' || interaction.type === 'submit') {
          await this.waitForPageLoad(webContents);
        }
      }

      console.log('[Spotify Replayer] Replay completed successfully');
      return true;
    } catch (error) {
      console.error('[Spotify Replayer] Replay failed:', error);
      return false;
    } finally {
      this.isReplaying = false;
    }
  }

  /**
   * Execute a single interaction
   * @param {Object} interaction - Interaction to execute
   * @param {Object} webContents - Electron webContents object
   * @returns {Promise<boolean>} Success status
   */
  async executeInteraction(interaction, webContents) {
    try {
      switch (interaction.type) {
        case 'click':
          return await this.executeClick(interaction, webContents);
        case 'input':
          return await this.executeInput(interaction, webContents);
        case 'submit':
          return await this.executeSubmit(interaction, webContents);
        case 'navigation':
          return await this.executeNavigation(interaction, webContents);
        default:
          console.warn(`[Spotify Replayer] Unknown interaction type: ${interaction.type}`);
          return false;
      }
    } catch (error) {
      console.error(`[Spotify Replayer] Error executing ${interaction.type}:`, error);
      return false;
    }
  }

  /**
   * Execute a click interaction
   * @param {Object} interaction - Click interaction
   * @param {Object} webContents - Electron webContents object
   * @returns {Promise<boolean>} Success status
   */
  async executeClick(interaction, webContents) {
    const result = await webContents.executeJavaScript(`
      (function() {
        const element = window.spotifyReplayUtils.findElement('${interaction.selector}', '${interaction.text}');
        if (element) {
          element.click();
          return true;
        }
        return false;
      })();
    `);

    return result;
  }

  /**
   * Execute an input interaction with enhanced form element support
   * @param {Object} interaction - Input interaction
   * @param {Object} webContents - Electron webContents object
   * @returns {Promise<boolean>} Success status
   */
  async executeInput(interaction, webContents) {
    const result = await webContents.executeJavaScript(`
      (function() {
        const element = window.spotifyReplayUtils.findElement('${interaction.selector}');
        if (!element) return false;

        const tagName = element.tagName.toLowerCase();
        const inputType = element.type ? element.type.toLowerCase() : '';
        const value = '${interaction.value.replace(/'/g, "\\'")}';

        // Handle different input types
        if (tagName === 'input' || tagName === 'textarea') {
          element.focus();

          // Special handling for different input types
          switch (inputType) {
            case 'checkbox':
            case 'radio':
              element.checked = value === 'true' || value === 'on' || value === '1';
              element.dispatchEvent(new Event('change', { bubbles: true }));
              break;

            case 'file':
              // File inputs can't be set programmatically for security reasons
              console.warn('Cannot set file input value programmatically');
              return false;

            case 'range':
              element.value = value;
              element.dispatchEvent(new Event('input', { bubbles: true }));
              element.dispatchEvent(new Event('change', { bubbles: true }));
              break;

            default:
              // Text, password, email, etc.
              element.value = value;
              element.dispatchEvent(new Event('input', { bubbles: true }));
              element.dispatchEvent(new Event('change', { bubbles: true }));
              element.dispatchEvent(new Event('keyup', { bubbles: true }));
              break;
          }

          return true;
        }

        // Handle select elements
        if (tagName === 'select') {
          element.value = value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          return true;
        }

        // Handle contenteditable elements
        if (element.contentEditable === 'true') {
          element.focus();
          element.textContent = value;
          element.dispatchEvent(new Event('input', { bubbles: true }));
          return true;
        }

        return false;
      })();
    `);

    return result;
  }

  /**
   * Execute a form submission
   * @param {Object} interaction - Submit interaction
   * @param {Object} webContents - Electron webContents object
   * @returns {Promise<boolean>} Success status
   */
  async executeSubmit(interaction, webContents) {
    const result = await webContents.executeJavaScript(`
      (function() {
        const form = window.spotifyReplayUtils.findElement('${interaction.selector}');
        if (form && form.tagName.toLowerCase() === 'form') {
          form.submit();
          return true;
        }
        
        // Try to find and click submit button
        const submitButton = form ? form.querySelector('button[type="submit"], input[type="submit"]') : null;
        if (submitButton) {
          submitButton.click();
          return true;
        }
        
        return false;
      })();
    `);

    return result;
  }

  /**
   * Execute navigation
   * @param {Object} interaction - Navigation interaction
   * @param {Object} webContents - Electron webContents object
   * @returns {Promise<boolean>} Success status
   */
  async executeNavigation(interaction, webContents) {
    try {
      if (interaction.toUrl) {
        webContents.loadURL(interaction.toUrl);
        return true;
      }
      return false;
    } catch (error) {
      console.error('[Spotify Replayer] Navigation error:', error);
      return false;
    }
  }

  /**
   * Inject replay utilities into the web page
   * @param {Object} webContents - Electron webContents object
   */
  async injectReplayScript(webContents) {
    await webContents.executeJavaScript(`
      (function() {
        if (window.spotifyReplayUtils) {
          return; // Already injected
        }

        window.spotifyReplayUtils = {
          // Find element using various strategies
          findElement: function(selector, text) {
            let element = null;
            
            // Try direct selector first
            try {
              element = document.querySelector(selector);
              if (element) return element;
            } catch (e) {
              console.warn('Invalid selector:', selector);
            }
            
            // Try alternative selectors based on common patterns
            const alternatives = this.generateAlternativeSelectors(selector);
            for (const altSelector of alternatives) {
              try {
                element = document.querySelector(altSelector);
                if (element) return element;
              } catch (e) {
                // Continue to next alternative
              }
            }
            
            // Try finding by text content if provided
            if (text) {
              const elements = document.querySelectorAll('*');
              for (const el of elements) {
                if (el.textContent && el.textContent.trim().includes(text.trim())) {
                  return el;
                }
              }
            }
            
            return null;
          },
          
          // Generate alternative selectors
          generateAlternativeSelectors: function(originalSelector) {
            const alternatives = [];

            // If it's an ID selector, try without the hash
            if (originalSelector.startsWith('#')) {
              const id = originalSelector.substring(1);
              alternatives.push('[id="' + id + '"]');
              alternatives.push('[id*="' + id + '"]');
            }

            // If it's a class selector, try variations
            if (originalSelector.startsWith('.')) {
              const className = originalSelector.substring(1);
              alternatives.push('[class*="' + className + '"]');
              alternatives.push('[class~="' + className + '"]');
            }

            // Common login form selectors
            if (originalSelector.includes('username') || originalSelector.includes('email')) {
              alternatives.push('input[type="email"]');
              alternatives.push('input[name*="user"]');
              alternatives.push('input[name*="email"]');
              alternatives.push('input[placeholder*="email"]');
              alternatives.push('input[placeholder*="username"]');
              alternatives.push('input[autocomplete*="username"]');
              alternatives.push('input[autocomplete*="email"]');
            }

            if (originalSelector.includes('password')) {
              alternatives.push('input[type="password"]');
              alternatives.push('input[name*="pass"]');
              alternatives.push('input[placeholder*="password"]');
              alternatives.push('input[autocomplete*="password"]');
            }

            if (originalSelector.includes('login') || originalSelector.includes('submit')) {
              alternatives.push('button[type="submit"]');
              alternatives.push('input[type="submit"]');
              alternatives.push('button[data-testid*="login"]');
              alternatives.push('button[data-testid*="submit"]');
              alternatives.push('.login-button');
              alternatives.push('.btn-primary');
              alternatives.push('.btn-green');
              alternatives.push('.btn-spotify');
              alternatives.push('[role="button"]');
            }

            // Handle checkboxes and radio buttons
            if (originalSelector.includes('checkbox') || originalSelector.includes('radio')) {
              alternatives.push('input[type="checkbox"]');
              alternatives.push('input[type="radio"]');
              alternatives.push('[role="checkbox"]');
              alternatives.push('[role="radio"]');
            }

            // Handle dropdowns/selects
            if (originalSelector.includes('select') || originalSelector.includes('dropdown')) {
              alternatives.push('select');
              alternatives.push('[role="combobox"]');
              alternatives.push('[role="listbox"]');
              alternatives.push('.dropdown');
              alternatives.push('.select');
            }

            return alternatives;
          },
          
          // Wait for element to appear
          waitForElement: function(selector, timeout = 5000) {
            return new Promise((resolve) => {
              const element = this.findElement(selector);
              if (element) {
                resolve(element);
                return;
              }
              
              const observer = new MutationObserver(() => {
                const element = this.findElement(selector);
                if (element) {
                  observer.disconnect();
                  resolve(element);
                }
              });
              
              observer.observe(document.body, {
                childList: true,
                subtree: true
              });
              
              setTimeout(() => {
                observer.disconnect();
                resolve(null);
              }, timeout);
            });
          }
        };
        
        console.log('[Spotify Replayer] Replay utilities injected');
      })();
    `);
  }

  /**
   * Wait for page to load with enhanced navigation detection
   * @param {Object} webContents - Electron webContents object
   * @param {number} timeout - Timeout in milliseconds
   */
  async waitForPageLoad(webContents, timeout = 10000) {
    const startTime = Date.now();
    let lastUrl = '';

    try {
      lastUrl = webContents.getURL();
    } catch (error) {
      // WebContents might be destroyed
      return;
    }

    while (Date.now() - startTime < timeout) {
      try {
        // Check if URL has changed (navigation occurred)
        const currentUrl = webContents.getURL();
        const urlChanged = currentUrl !== lastUrl;

        // Check document ready state
        const pageState = await webContents.executeJavaScript(`
          ({
            readyState: document.readyState,
            url: window.location.href,
            hasSpinner: !!(document.querySelector('.loading') ||
                          document.querySelector('.spinner') ||
                          document.querySelector('[data-testid*="loading"]')),
            hasLoginForm: !!(document.querySelector('#login-username') ||
                            document.querySelector('input[type="email"]') ||
                            document.querySelector('input[name*="user"]')),
            hasErrorMessage: !!(document.querySelector('.error') ||
                               document.querySelector('.alert-error') ||
                               document.querySelector('[data-testid*="error"]'))
          })
        `);

        // Page is loaded if:
        // 1. Document is complete
        // 2. No loading spinners
        // 3. URL has stabilized (no recent changes)
        if (pageState.readyState === 'complete' &&
            !pageState.hasSpinner &&
            !urlChanged) {

          // Additional delay for dynamic content and animations
          await this.delay(1000);

          // Final check for stability
          const finalUrl = webContents.getURL();
          if (finalUrl === currentUrl) {
            console.log(`[Replayer] Page loaded: ${finalUrl}`);
            return;
          }
        }

        lastUrl = currentUrl;
        await this.delay(200);

      } catch (error) {
        // Page might be navigating or webContents destroyed
        console.warn('[Replayer] Error checking page load status:', error.message);
        await this.delay(500);
      }
    }

    console.warn('[Replayer] Page load timeout reached');
  }

  /**
   * Handle multi-step authentication flows
   * @param {Object} webContents - Electron webContents object
   * @returns {Promise<string>} Current authentication step
   */
  async detectAuthenticationStep(webContents) {
    try {
      const authStep = await webContents.executeJavaScript(`
        (function() {
          const url = window.location.href;

          // Check for different authentication steps
          if (url.includes('login') || url.includes('signin')) {
            if (document.querySelector('input[type="password"]')) {
              return 'password_step';
            } else if (document.querySelector('input[type="email"]') ||
                      document.querySelector('input[name*="user"]')) {
              return 'username_step';
            }
            return 'login_page';
          }

          if (url.includes('2fa') || url.includes('two-factor') ||
              document.querySelector('input[name*="code"]') ||
              document.querySelector('input[placeholder*="code"]')) {
            return 'two_factor';
          }

          if (url.includes('captcha') || document.querySelector('.captcha')) {
            return 'captcha';
          }

          if (url.includes('consent') || url.includes('authorize') ||
              document.querySelector('button:contains("Accept")') ||
              document.querySelector('button:contains("Allow")')) {
            return 'consent';
          }

          if (url.includes('open.spotify.com') ||
              url.includes('spotify.com/track') ||
              url.includes('spotify.com/playlist')) {
            return 'logged_in';
          }

          return 'unknown';
        })();
      `);

      return authStep;
    } catch (error) {
      console.error('[Replayer] Error detecting auth step:', error);
      return 'unknown';
    }
  }

  /**
   * Stop current replay
   */
  stopReplay() {
    this.isReplaying = false;
    console.log('[Spotify Replayer] Replay stopped');
  }

  /**
   * Delay execution
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} Promise that resolves after delay
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get replay progress
   * @returns {Object} Progress information
   */
  getProgress() {
    return {
      isReplaying: this.isReplaying,
      currentIndex: this.currentInteractionIndex,
      elapsedTime: this.replayStartTime ? Date.now() - this.replayStartTime : 0
    };
  }
}

module.exports = SpotifyInteractionReplayer;
