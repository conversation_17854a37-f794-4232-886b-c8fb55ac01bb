const executionCollector = require('./execution-collector');

/**
 * VideoProgressMonitor - Monitors video playback progress and triggers actions
 * when videos reach 90-95% completion at random intervals
 */
class VideoProgressMonitor {
  constructor() {
    this.isEnabled = true;
    this.webContents = null;
    this.checkInterval = null;
    this.progressCheckInterval = 2000; // Check every 2 seconds
    this.currentVideoUrl = null;
    this.currentVideoData = null;
    this.switchThreshold = null; // Will be set randomly between 90-95%
    this.onVideoSwitchCallback = null;
    this.lastProgressCheck = 0;
    this.progressHistory = [];
    this.videoStartTime = null;
    this.hasTriggeredSwitch = false;
  }

  /**
   * Register web contents for progress monitoring
   * @param {Electron.WebContents} webContents - The YouTube web contents
   */
  registerWebContents(webContents) {
    this.webContents = webContents;
    console.log('Video Progress Monitor: Web contents registered');
    this.startProgressMonitoring();
  }

  /**
   * Unregister web contents
   */
  unregisterWebContents() {
    this.stopProgressMonitoring();
    this.webContents = null;
    console.log('Video Progress Monitor: Web contents unregistered');
  }

  /**
   * Set callback function to be called when video should switch
   * @param {Function} callback - Function to call when switching videos
   */
  setVideoSwitchCallback(callback) {
    this.onVideoSwitchCallback = callback;
  }

  /**
   * Start monitoring video progress
   */
  startProgressMonitoring() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    if (!this.isEnabled || !this.webContents) {
      return;
    }

    console.log('Video Progress Monitor: Starting progress monitoring');
    
    this.checkInterval = setInterval(() => {
      this.checkVideoProgress();
    }, this.progressCheckInterval);
  }

  /**
   * Stop monitoring video progress
   */
  stopProgressMonitoring() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log('Video Progress Monitor: Stopped progress monitoring');
    }
  }

  /**
   * Set current video information
   * @param {string} url - Video URL
   * @param {Object} videoData - Video metadata
   */
  setCurrentVideo(url, videoData = null) {
    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      this.currentVideoUrl = url;
      this.currentVideoData = videoData;
      this.videoStartTime = Date.now();
      this.hasTriggeredSwitch = false;
      this.progressHistory = [];
      
      // Set random switch threshold between 90-95%
      this.switchThreshold = 90 + Math.random() * 5; // Random between 90.0 and 95.0
      
      result = {
        success: true,
        url: url,
        switchThreshold: this.switchThreshold,
        startTime: this.videoStartTime
      };
      
      console.log(`Video Progress Monitor: Set current video to ${url}, switch threshold: ${this.switchThreshold.toFixed(2)}%`);
      return result;
    } catch (err) {
      error = err;
      throw err;
    } finally {
      executionCollector.collect(
        'setCurrentVideo',
        { url, videoData },
        result,
        error,
        {
          executionTime: Date.now() - startTime,
          switchThreshold: this.switchThreshold
        }
      );
    }
  }

  /**
   * Check current video progress
   */
  async checkVideoProgress() {
    if (!this.webContents || this.webContents.isDestroyed() || !this.currentVideoUrl) {
      return;
    }

    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      const progressInfo = await this.webContents.executeJavaScript(`
        (function() {
          console.log('Video Progress Monitor: Checking video progress...');
          
          const video = document.querySelector('video');
          if (!video) {
            return {
              hasVideo: false,
              error: 'No video element found'
            };
          }
          
          const currentTime = video.currentTime;
          const duration = video.duration;
          const progress = duration > 0 ? (currentTime / duration) * 100 : 0;
          const isPaused = video.paused;
          const isEnded = video.ended;
          const readyState = video.readyState;
          const networkState = video.networkState;
          
          // Get video title if available
          const titleElement = document.querySelector('h1.title') || 
                              document.querySelector('.ytp-title-text') ||
                              document.querySelector('#container h1') ||
                              document.querySelector('meta[property="og:title"]');
          
          const title = titleElement ? 
            (titleElement.content || titleElement.textContent || titleElement.innerText || '').trim() : 
            'Unknown';
          
          // Check if we're on a video page
          const isVideoPage = window.location.href.includes('/watch?v=');
          
          return {
            hasVideo: true,
            currentTime: currentTime,
            duration: duration,
            progress: progress,
            isPaused: isPaused,
            isEnded: isEnded,
            readyState: readyState,
            networkState: networkState,
            title: title,
            url: window.location.href,
            isVideoPage: isVideoPage,
            timestamp: Date.now()
          };
        })();
      `);

      result = progressInfo;

      if (progressInfo.hasVideo && progressInfo.progress > 0) {
        // Store progress in history
        this.progressHistory.push({
          progress: progressInfo.progress,
          timestamp: Date.now(),
          currentTime: progressInfo.currentTime,
          duration: progressInfo.duration
        });

        // Keep only last 10 progress entries
        if (this.progressHistory.length > 10) {
          this.progressHistory = this.progressHistory.slice(-10);
        }

        console.log(`Video Progress: ${progressInfo.progress ? progressInfo.progress.toFixed(2) : 'N/A'}% (${progressInfo.currentTime ? progressInfo.currentTime.toFixed(1) : 'N/A'}s / ${progressInfo.duration ? progressInfo.duration.toFixed(1) : 'N/A'}s) - Threshold: ${this.switchThreshold ? this.switchThreshold.toFixed(2) : 'N/A'}%`);

        // Check if we should switch videos
        if (!this.hasTriggeredSwitch && 
            progressInfo.progress >= this.switchThreshold && 
            !progressInfo.isPaused && 
            !progressInfo.isEnded) {
          
          this.hasTriggeredSwitch = true;
          console.log(`Video Progress Monitor: Triggering video switch at ${progressInfo.progress.toFixed(2)}%`);
          
          if (this.onVideoSwitchCallback) {
            this.onVideoSwitchCallback(progressInfo);
          }
        }
      }

      this.lastProgressCheck = Date.now();

    } catch (err) {
      error = err;
      console.error('Video Progress Monitor: Error checking progress:', err);
    } finally {
      executionCollector.collect(
        'checkVideoProgress',
        { currentVideoUrl: this.currentVideoUrl },
        result,
        error,
        {
          executionTime: Date.now() - startTime,
          switchThreshold: this.switchThreshold,
          hasTriggeredSwitch: this.hasTriggeredSwitch,
          progressHistoryLength: this.progressHistory.length
        }
      );
    }
  }

  /**
   * Get current progress statistics
   */
  getProgressStats() {
    return {
      isEnabled: this.isEnabled,
      currentVideoUrl: this.currentVideoUrl,
      switchThreshold: this.switchThreshold,
      hasTriggeredSwitch: this.hasTriggeredSwitch,
      progressHistoryLength: this.progressHistory.length,
      lastProgressCheck: this.lastProgressCheck,
      videoStartTime: this.videoStartTime
    };
  }

  /**
   * Get progress history
   */
  getProgressHistory() {
    return [...this.progressHistory];
  }

  /**
   * Enable progress monitoring
   */
  enable() {
    this.isEnabled = true;
    console.log('Video Progress Monitor: Enabled');
    if (this.webContents) {
      this.startProgressMonitoring();
    }
  }

  /**
   * Disable progress monitoring
   */
  disable() {
    this.isEnabled = false;
    this.stopProgressMonitoring();
    console.log('Video Progress Monitor: Disabled');
  }

  /**
   * Reset monitoring state for new video
   */
  reset() {
    this.hasTriggeredSwitch = false;
    this.progressHistory = [];
    this.switchThreshold = 90 + Math.random() * 5;
    this.videoStartTime = Date.now();
    console.log(`Video Progress Monitor: Reset with new threshold: ${this.switchThreshold.toFixed(2)}%`);
  }
}

module.exports = VideoProgressMonitor;
