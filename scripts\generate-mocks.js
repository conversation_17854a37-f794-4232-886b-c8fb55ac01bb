#!/usr/bin/env node

/**
 * <PERSON>ript to generate mock data for tests by running the application in dev mode
 * and collecting execution information
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const MockDataGenerator = require('../tests/mock-data-generator');

console.log('🚀 Starting mock data generation...');

// Ensure the mock data directory exists
const mockDataDir = path.join(__dirname, '../tests/mock-data');
if (!fs.existsSync(mockDataDir)) {
  fs.mkdirSync(mockDataDir, { recursive: true });
}

// Function to run the app in dev mode for a short time to collect data
function runAppForMockGeneration() {
  return new Promise((resolve, reject) => {
    console.log('📱 Starting application in dev mode...');
    
    const appProcess = spawn('npm', ['run', 'dev'], {
      cwd: path.join(__dirname, '..'),
      stdio: 'pipe'
    });

    let output = '';
    let errorOutput = '';

    appProcess.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log('📊 App output:', text.trim());
    });

    appProcess.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      console.log('⚠️ App error:', text.trim());
    });

    // Let the app run for 10 seconds to collect some execution data
    const timeout = setTimeout(() => {
      console.log('⏰ Stopping application after 10 seconds...');
      appProcess.kill('SIGTERM');
    }, 10000);

    appProcess.on('close', (code) => {
      clearTimeout(timeout);
      console.log(`📱 Application exited with code ${code}`);
      
      if (code === 0 || code === null) {
        resolve({ output, errorOutput });
      } else {
        reject(new Error(`Application exited with code ${code}\nError: ${errorOutput}`));
      }
    });

    appProcess.on('error', (error) => {
      clearTimeout(timeout);
      reject(error);
    });
  });
}

// Function to simulate some user interactions
async function simulateUserInteractions() {
  console.log('🎭 Simulating user interactions...');
  
  // This would ideally use automation tools like Puppeteer or Playwright
  // For now, we'll just let the app run and collect whatever execution data it generates
  
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('✅ User interaction simulation complete');
      resolve();
    }, 5000);
  });
}

// Main function
async function main() {
  try {
    console.log('🔧 Mock Data Generation Started');
    console.log('================================');
    
    // Step 1: Run the application in dev mode
    await runAppForMockGeneration();
    
    // Step 2: Check if execution data was collected
    const summaryPath = path.join(mockDataDir, 'summary.json');
    if (fs.existsSync(summaryPath)) {
      const summary = JSON.parse(fs.readFileSync(summaryPath, 'utf8'));
      console.log(`📈 Collected execution data for ${summary.totalFunctions} functions`);
      console.log(`📊 Total executions: ${summary.totalExecutions}`);
      console.log(`🔍 Functions: ${summary.functions.join(', ')}`);
    } else {
      console.log('⚠️ No execution data summary found. The app may not have run long enough.');
    }
    
    // Step 3: Generate Jest mock files
    console.log('🏭 Generating Jest mock files...');
    const generator = new MockDataGenerator();
    generator.generateAllMocks();
    
    console.log('✅ Mock data generation complete!');
    console.log('📁 Check the tests/mock-data and tests/generated-mocks directories');
    
  } catch (error) {
    console.error('❌ Mock data generation failed:', error);
    process.exit(1);
  }
}

// Handle script termination
process.on('SIGINT', () => {
  console.log('\n🛑 Mock data generation interrupted');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Mock data generation terminated');
  process.exit(0);
});

// Run the main function
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = { runAppForMockGeneration, simulateUserInteractions };
