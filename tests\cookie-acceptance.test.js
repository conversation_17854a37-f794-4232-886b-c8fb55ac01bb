const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');

// Mock objects
const mockStore = {
  get: jest.fn(),
  set: jest.fn(),
  delete: jest.fn()
};

const mockWebContents = {
  executeJavaScript: jest.fn(),
  isDestroyed: jest.fn(() => false)
};

// Mock electron-store
jest.mock('electron-store', () => {
  return jest.fn(() => mockStore);
});

jest.mock('electron', () => ({
  app: {
    whenReady: jest.fn(() => Promise.resolve()),
    on: jest.fn(),
    getPath: jest.fn(() => '/mock/path'),
    isQuiting: false,
    quit: jest.fn()
  },
  BrowserWindow: jest.fn(),
  BrowserView: jest.fn(),
  Tray: jest.fn(),
  Menu: {
    buildFromTemplate: jest.fn()
  },
  ipcMain: {
    handle: jest.fn()
  },
  screen: {
    getPrimaryDisplay: jest.fn(() => ({
      workAreaSize: { width: 1920, height: 1080 }
    }))
  },
  shell: {
    openExternal: jest.fn()
  }
}));

// Mock other dependencies
jest.mock('auto-launch', () => {
  return jest.fn(() => ({
    isEnabled: jest.fn(() => Promise.resolve(false)),
    enable: jest.fn(() => Promise.resolve())
  }));
});

jest.mock('../src/playlist-manager', () => {
  return jest.fn(() => ({
    initialize: jest.fn(() => Promise.resolve()),
    getNextMandatoryVideo: jest.fn(() => Promise.resolve(null)),
    shouldPlayNext: jest.fn(() => Promise.resolve(false))
  }));
});

jest.mock('../src/audio-controller', () => {
  return jest.fn(() => ({
    setMute: jest.fn(() => Promise.resolve()),
    toggleMute: jest.fn(() => Promise.resolve(true)),
    registerWebContents: jest.fn()
  }));
});

// Import the main module after mocking
const path = require('path');

describe('Cookie Acceptance Functionality', () => {
  let YSViewerApp;
  let appInstance;

  beforeEach(() => {
    jest.clearAllMocks();

    // Since the class is not exported, we'll create a mock implementation
    YSViewerApp = class TestYSViewerApp {
      constructor() {
        this.store = mockStore;
      }

      async autoAcceptCookies(webContents, platform) {
        try {
          // Check if cookies have already been accepted for this platform
          const cookieAcceptanceKey = `cookiesAccepted_${platform}`;
          const alreadyAccepted = this.store.get(cookieAcceptanceKey, false);

          if (alreadyAccepted) {
            console.log(`Cookies already accepted for ${platform}, skipping auto-accept`);
            return;
          }

          console.log(`Attempting to auto-accept cookies for ${platform}`);

          let cookieAccepted = false;

        if (platform === 'YouTube') {
          cookieAccepted = await webContents.executeJavaScript(`
            (function() {
              const selectors = [
                'button[aria-label*="Accept"]',
                'button[aria-label*="accept"]',
                'button:contains("Accept all")',
                'button:contains("I agree")',
                'button:contains("Accept")',
                '[data-testid="accept-button"]',
                '.VfPpkd-LgbsSe[jsname="tWT92d"]',
                'button[jsname="tWT92d"]'
              ];

              for (let selector of selectors) {
                const button = document.querySelector(selector);
                if (button && button.offsetParent !== null) {
                  console.log('Found YouTube cookie accept button:', selector);
                  button.click();
                  return true;
                }
              }

              const buttons = document.querySelectorAll('button');
              for (let button of buttons) {
                const text = button.textContent.toLowerCase();
                if (text.includes('accept') || text.includes('agree') || text.includes('allow')) {
                  console.log('Found YouTube cookie button by text:', text);
                  button.click();
                  return true;
                }
              }

              return false;
            })();
          `);
        } else if (platform === 'Spotify') {
          cookieAccepted = await webContents.executeJavaScript(`
            (function() {
              const selectors = [
                'button[data-testid="accept-all-cookies"]',
                'button[id*="accept"]',
                'button:contains("Accept all")',
                'button:contains("Accept cookies")',
                'button:contains("I agree")',
                'button:contains("Accept")',
                '.onetrust-accept-btn-handler',
                '#onetrust-accept-btn-handler',
                '[data-cy="accept-all-cookies"]'
              ];

              for (let selector of selectors) {
                const button = document.querySelector(selector);
                if (button && button.offsetParent !== null) {
                  console.log('Found Spotify cookie accept button:', selector);
                  button.click();
                  return true;
                }
              }

              const buttons = document.querySelectorAll('button');
              for (let button of buttons) {
                const text = button.textContent.toLowerCase();
                if (text.includes('accept') || text.includes('agree') || text.includes('allow')) {
                  console.log('Found Spotify cookie button by text:', text);
                  button.click();
                  return true;
                }
              }

              return false;
            })();
          `);
        }

        // If cookies were successfully accepted, mark it as completed
        if (cookieAccepted) {
          this.store.set(cookieAcceptanceKey, true);
          console.log(`Cookies accepted for ${platform} - marked as completed, will not try again`);
        }
      } catch (error) {
        console.log(`Failed to auto-accept cookies for ${platform}:`, error.message);
      }
    }

    resetCookieAcceptance(platform = null) {
        if (platform) {
          const cookieAcceptanceKey = `cookiesAccepted_${platform}`;
          this.store.delete(cookieAcceptanceKey);
          console.log(`Reset cookie acceptance flag for ${platform}`);
        } else {
          this.store.delete('cookiesAccepted_YouTube');
          this.store.delete('cookiesAccepted_Spotify');
          console.log('Reset cookie acceptance flags for all platforms');
        }
      }
    };

    appInstance = new YSViewerApp();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('autoAcceptCookies', () => {
    test('should skip cookie acceptance if already accepted for YouTube', async () => {
      mockStore.get.mockReturnValue(true); // Already accepted
      
      await appInstance.autoAcceptCookies(mockWebContents, 'YouTube');
      
      expect(mockStore.get).toHaveBeenCalledWith('cookiesAccepted_YouTube', false);
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should skip cookie acceptance if already accepted for Spotify', async () => {
      mockStore.get.mockReturnValue(true); // Already accepted
      
      await appInstance.autoAcceptCookies(mockWebContents, 'Spotify');
      
      expect(mockStore.get).toHaveBeenCalledWith('cookiesAccepted_Spotify', false);
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should attempt cookie acceptance for YouTube when not already accepted', async () => {
      mockStore.get.mockReturnValue(false); // Not accepted yet
      mockWebContents.executeJavaScript.mockResolvedValue(true); // Cookie button found and clicked
      
      await appInstance.autoAcceptCookies(mockWebContents, 'YouTube');
      
      expect(mockStore.get).toHaveBeenCalledWith('cookiesAccepted_YouTube', false);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalled();
      expect(mockStore.set).toHaveBeenCalledWith('cookiesAccepted_YouTube', true);
    });

    test('should attempt cookie acceptance for Spotify when not already accepted', async () => {
      mockStore.get.mockReturnValue(false); // Not accepted yet
      mockWebContents.executeJavaScript.mockResolvedValue(true); // Cookie button found and clicked
      
      await appInstance.autoAcceptCookies(mockWebContents, 'Spotify');
      
      expect(mockStore.get).toHaveBeenCalledWith('cookiesAccepted_Spotify', false);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalled();
      expect(mockStore.set).toHaveBeenCalledWith('cookiesAccepted_Spotify', true);
    });

    test('should not mark as accepted if cookie button was not found', async () => {
      mockStore.get.mockReturnValue(false); // Not accepted yet
      mockWebContents.executeJavaScript.mockResolvedValue(false); // Cookie button not found
      
      await appInstance.autoAcceptCookies(mockWebContents, 'YouTube');
      
      expect(mockStore.get).toHaveBeenCalledWith('cookiesAccepted_YouTube', false);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalled();
      expect(mockStore.set).not.toHaveBeenCalled();
    });

    test('should handle JavaScript execution errors gracefully', async () => {
      mockStore.get.mockReturnValue(false); // Not accepted yet
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('JavaScript error'));

      // Should not throw an error - function should handle the error gracefully
      await expect(appInstance.autoAcceptCookies(mockWebContents, 'YouTube')).resolves.toBeUndefined();

      expect(mockStore.set).not.toHaveBeenCalled();
    });
  });

  describe('resetCookieAcceptance', () => {
    test('should reset cookie acceptance for specific platform', () => {
      appInstance.resetCookieAcceptance('YouTube');
      
      expect(mockStore.delete).toHaveBeenCalledWith('cookiesAccepted_YouTube');
      expect(mockStore.delete).toHaveBeenCalledTimes(1);
    });

    test('should reset cookie acceptance for all platforms when no platform specified', () => {
      appInstance.resetCookieAcceptance();
      
      expect(mockStore.delete).toHaveBeenCalledWith('cookiesAccepted_YouTube');
      expect(mockStore.delete).toHaveBeenCalledWith('cookiesAccepted_Spotify');
      expect(mockStore.delete).toHaveBeenCalledTimes(2);
    });
  });
});
